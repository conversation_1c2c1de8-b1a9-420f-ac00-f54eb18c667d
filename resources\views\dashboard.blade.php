@extends('layouts.app')

@section('title', 'Dashboard - SiPanda')

@section('breadcrumb')
    <span>SiPanda</span>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <span class="text-gray-900 font-medium">Dashboard</span>
@endsection

@section('content')
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
                <p class="text-gray-600 mt-1">
                    Selamat datang di SiPanda, {{ auth()->user()->name }}
                    @if (auth()->user()->isAdmin())
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                            Admin
                        </span>
                    @elseif(auth()->user()->isDesa())
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
                            Desa
                        </span>
                    @endif
                </p>
            </div>
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-sm text-gray-600">Online</span>
            </div>
        </div>

        @if (auth()->user()->isAdmin())
            <!-- Admin Dashboard -->
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Desa -->
                <div class="bg-blue-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-600 text-sm font-medium">Total Desa</p>
                            <p class="text-2xl font-bold text-blue-900">
                                {{ \App\Models\User::where('role', 'desa')->count() }}</p>
                            <p class="text-xs text-blue-600 mt-1">Desa terdaftar</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total SPJ -->
                <div class="bg-green-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-600 text-sm font-medium">Total SPJ</p>
                            <p class="text-2xl font-bold text-green-900">{{ \App\Models\Spj::count() }}</p>
                            <p class="text-xs text-green-600 mt-1">SPJ terdaftar</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Anggaran -->
                <div class="bg-yellow-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-yellow-600 text-sm font-medium">Total Anggaran</p>
                            <p class="text-xl font-bold text-yellow-900">
                                Rp {{ number_format(\App\Models\Spj::sum('anggaran') / 1000000000, 1) }}M
                            </p>
                            <p class="text-xs text-yellow-600 mt-1">Anggaran keseluruhan</p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Dokumen -->
                <div class="bg-purple-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-600 text-sm font-medium">Total Dokumen</p>
                            <p class="text-2xl font-bold text-purple-900">{{ \App\Models\SpjDocument::count() }}</p>
                            <p class="text-xs text-purple-600 mt-1">Dokumen diupload</p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                <!-- SPJ Status -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Status SPJ</h3>
                    <div class="flex items-center justify-center mb-4" style="height: 200px;">
                        <canvas id="adminSpjChart"></canvas>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Pending</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('status_kelengkapan', 'pending')->count() }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Lengkap</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('status_kelengkapan', 'lengkap')->count() }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Belum Lengkap</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('status_kelengkapan', 'belum_lengkap')->count() }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Completion Rate</span>
                            <span class="font-medium text-green-600">
                                {{ \App\Models\Spj::count() > 0 ? round((\App\Models\Spj::where('status_kelengkapan', 'lengkap')->count() / \App\Models\Spj::count()) * 100, 1) : 0 }}%
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Aktivitas Terbaru</h3>
                    <div class="space-y-3">
                        @php
                            $recentSpjs = \App\Models\Spj::with('user')->latest()->take(5)->get();
                        @endphp
                        @forelse($recentSpjs as $spj)
                            <div class="flex items-center space-x-3">
                                <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm text-gray-900 truncate">{{ $spj->nama_kegiatan }}</p>
                                    <p class="text-xs text-gray-500">{{ $spj->user->name }} •
                                        {{ $spj->created_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500">Belum ada aktivitas</p>
                        @endforelse
                    </div>
                </div>

                <!-- Top Desa -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Desa</h3>
                    <div class="space-y-3">
                        @php
                            $topDesa = \App\Models\User::where('role', 'desa')
                                ->withCount('spjs')
                                ->orderBy('spjs_count', 'desc')
                                ->take(5)
                                ->get();
                        @endphp
                        @forelse($topDesa as $index => $desa)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">{{ $index + 1 }}</span>
                                    </div>
                                    <span class="text-sm text-gray-900">{{ $desa->name }}</span>
                                </div>
                                <span class="text-sm font-medium text-gray-600">{{ $desa->spjs_count }} SPJ</span>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500">Belum ada data</p>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Quick Actions for Admin -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <a href="{{ route('desa.index') }}"
                        class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Kelola Desa</p>
                        </div>
                    </a>
                    <a href="{{ route('desa.create') }}"
                        class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Tambah Desa</p>
                        </div>
                    </a>
                    <a href="{{ route('monitoring.spj.index') }}"
                        class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Monitoring SPJ</p>
                        </div>
                    </a>
                    <button class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Laporan</p>
                        </div>
                    </button>
                </div>
            </div>
        @elseif(auth()->user()->isDesa())
            <!-- Desa Dashboard -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- SPJ Saya -->
                <div class="bg-blue-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-600 text-sm font-medium">SPJ Saya</p>
                            <p class="text-2xl font-bold text-blue-900">
                                {{ \App\Models\Spj::where('user_id', auth()->id())->count() }}</p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Dokumen Uploaded -->
                <div class="bg-green-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-600 text-sm font-medium">Dokumen Uploaded</p>
                            <p class="text-2xl font-bold text-green-900">
                                {{ \App\Models\SpjDocument::where('uploaded_by', auth()->id())->count() }}</p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Total Anggaran -->
                <div class="bg-yellow-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-yellow-600 text-sm font-medium">Total Anggaran</p>
                            <p class="text-2xl font-bold text-yellow-900">
                                Rp
                                {{ number_format(\App\Models\Spj::where('user_id', auth()->id())->sum('anggaran'), 0, ',', '.') }}
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>

                <!-- Status SPJ -->
                <div class="bg-purple-50 rounded-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-600 text-sm font-medium">SPJ Lengkap</p>
                            <p class="text-2xl font-bold text-purple-900">
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'lengkap')->count() }}
                            </p>
                            <p class="text-xs text-purple-600 mt-1">
                                dari {{ \App\Models\Spj::where('user_id', auth()->id())->count() }} total SPJ
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor"
                                viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Overview for Desa -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- My SPJ Status -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Status SPJ Saya</h3>
                    <div class="flex items-center justify-center mb-4" style="height: 200px;">
                        <canvas id="desaSpjChart"></canvas>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-yellow-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Pending</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'pending')->count() }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Lengkap</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'lengkap')->count() }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                                <span class="text-sm text-gray-600">Belum Lengkap</span>
                            </div>
                            <span class="text-sm font-medium text-gray-900">
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'belum_lengkap')->count() }}
                            </span>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Completion Rate</span>
                            <span class="font-medium text-green-600">
                                @php
                                    $myTotalSpj = \App\Models\Spj::where('user_id', auth()->id())->count();
                                    $myCompletedSpj = \App\Models\Spj::where('user_id', auth()->id())
                                        ->where('status_kelengkapan', 'lengkap')
                                        ->count();
                                @endphp
                                {{ $myTotalSpj > 0 ? round(($myCompletedSpj / $myTotalSpj) * 100, 1) : 0 }}%
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Recent SPJ -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">SPJ Terbaru Saya</h3>
                    <div class="space-y-3">
                        @php
                            $myRecentSpjs = \App\Models\Spj::where('user_id', auth()->id())
                                ->latest()
                                ->take(5)
                                ->get();
                        @endphp
                        @forelse($myRecentSpjs as $spj)
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm text-gray-900 truncate">{{ $spj->nama_kegiatan }}</p>
                                        <p class="text-xs text-gray-500">{{ $spj->created_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                                <span
                                    class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium {{ $spj->status_badge_class }}">
                                    {{ $spj->formatted_status }}
                                </span>
                            </div>
                        @empty
                            <p class="text-sm text-gray-500">Belum ada SPJ</p>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Quick Actions for Desa -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="{{ route('spj.index') }}"
                        class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Kelola SPJ</p>
                        </div>
                    </a>
                    <a href="{{ route('spj.create') }}"
                        class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Buat SPJ Baru</p>
                        </div>
                    </a>
                    <button class="bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                        <div class="text-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                            </div>
                            <p class="text-sm font-medium text-gray-900">Laporan SPJ</p>
                        </div>
                    </button>
                </div>
            </div>
        @endif
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if (auth()->user()->isAdmin())
                // Admin SPJ Chart
                const adminCtx = document.getElementById('adminSpjChart').getContext('2d');
                const adminSpjChart = new Chart(adminCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Pending', 'Lengkap', 'Belum Lengkap'],
                        datasets: [{
                            data: [
                                {{ \App\Models\Spj::where('status_kelengkapan', 'pending')->count() }},
                                {{ \App\Models\Spj::where('status_kelengkapan', 'lengkap')->count() }},
                                {{ \App\Models\Spj::where('status_kelengkapan', 'belum_lengkap')->count() }}
                            ],
                            backgroundColor: [
                                '#FBBF24', // Yellow for Pending
                                '#34D399', // Green for Lengkap
                                '#F87171' // Red for Belum Lengkap
                            ],
                            borderColor: [
                                '#F59E0B',
                                '#10B981',
                                '#EF4444'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((context.parsed / total) * 100)
                                            .toFixed(1) : 0;
                                        return context.label + ': ' + context.parsed + ' (' +
                                            percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            @elseif (auth()->user()->isDesa())
                // Desa SPJ Chart
                const desaCtx = document.getElementById('desaSpjChart').getContext('2d');
                const desaSpjChart = new Chart(desaCtx, {
                    type: 'pie',
                    data: {
                        labels: ['Pending', 'Lengkap', 'Belum Lengkap'],
                        datasets: [{
                            data: [
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'pending')->count() }},
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'lengkap')->count() }},
                                {{ \App\Models\Spj::where('user_id', auth()->id())->where('status_kelengkapan', 'belum_lengkap')->count() }}
                            ],
                            backgroundColor: [
                                '#FBBF24', // Yellow for Pending
                                '#34D399', // Green for Lengkap
                                '#F87171' // Red for Belum Lengkap
                            ],
                            borderColor: [
                                '#F59E0B',
                                '#10B981',
                                '#EF4444'
                            ],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((context.parsed / total) * 100)
                                            .toFixed(1) : 0;
                                        return context.label + ': ' + context.parsed + ' (' +
                                            percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            @endif
        });
    </script>
@endsection
