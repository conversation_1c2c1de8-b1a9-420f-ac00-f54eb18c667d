<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('spj_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('spj_id')->constrained()->onDelete('cascade');
            $table->enum('document_type', [
                'spp',
                'sptjb',
                'kwitansi_siskeudes',
                'tanda_terima',
                'photo',
                'nota',
                'pembayaran_pajak',
                'notulen_berita_acara',
                'absensi',
                'lainnya'
            ]);
            $table->string('document_name')->nullable(); // For 'lainnya' type
            $table->string('original_name');
            $table->string('file_name');
            $table->string('file_path');
            $table->string('file_size');
            $table->string('mime_type');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('spj_documents');
    }
};
