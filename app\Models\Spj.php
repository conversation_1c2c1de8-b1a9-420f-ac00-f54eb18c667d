<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Spj extends Model
{
    protected $fillable = [
        'nama_kegiatan',
        'tanggal_kegiatan',
        'anggaran',
        'keterangan',
        'status_kelengkapan',
        'user_id',
    ];

    protected $casts = [
        'tanggal_kegiatan' => 'date',
        'anggaran' => 'decimal:2',
    ];

    /**
     * Get the user that owns the SPJ.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Format anggaran as currency
     */
    public function getFormattedAnggaranAttribute(): string
    {
        return 'Rp ' . number_format($this->anggaran, 0, ',', '.');
    }

    /**
     * Get the documents for the SPJ.
     */
    public function documents(): HasMany
    {
        return $this->hasMany(SpjDocument::class);
    }

    /**
     * Get status kelengkapan options
     */
    public static function getStatusOptions(): array
    {
        return [
            'pending' => 'Pending',
            'lengkap' => 'Lengkap',
            'belum_lengkap' => 'Belum Lengkap',
        ];
    }

    /**
     * Get formatted status kelengkapan
     */
    public function getFormattedStatusAttribute(): string
    {
        return self::getStatusOptions()[$this->status_kelengkapan] ?? 'Pending';
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match ($this->status_kelengkapan) {
            'lengkap' => 'bg-green-100 text-green-800',
            'belum_lengkap' => 'bg-red-100 text-red-800',
            'pending' => 'bg-yellow-100 text-yellow-800',
            default => 'bg-gray-100 text-gray-800',
        };
    }
}
