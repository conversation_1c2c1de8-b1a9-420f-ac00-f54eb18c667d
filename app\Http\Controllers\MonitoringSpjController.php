<?php

namespace App\Http\Controllers;

use App\Models\Spj;
use App\Models\SpjDocument;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class MonitoringSpjController extends Controller
{
    /**
     * Display monitoring dashboard for admin
     */
    public function index(Request $request)
    {
        // Get filter parameters
        $search = $request->get('search');
        $desa_filter = $request->get('desa');
        $status_filter = $request->get('status');
        $date_from = $request->get('date_from');
        $date_to = $request->get('date_to');

        // Build query
        $query = Spj::with(['user', 'documents']);

        // Apply filters
        if ($search) {
            $query->where('nama_kegiatan', 'like', '%' . $search . '%');
        }

        if ($desa_filter) {
            $query->where('user_id', $desa_filter);
        }

        if ($date_from) {
            $query->where('tanggal_kegiatan', '>=', $date_from);
        }

        if ($date_to) {
            $query->where('tanggal_kegiatan', '<=', $date_to);
        }

        // Apply status filter (based on status_kelengkapan field)
        if ($status_filter) {
            $query->where('status_kelengkapan', $status_filter);
        }

        $spjs = $query->latest()->paginate(15);

        // Get statistics
        $stats = $this->getStatistics();

        // Get desa list for filter
        $desas = User::where('role', 'desa')->orderBy('name')->get();

        return view('monitoring.spj.index', compact('spjs', 'stats', 'desas', 'search', 'desa_filter', 'status_filter', 'date_from', 'date_to'));
    }

    /**
     * Show detailed view of SPJ for monitoring
     */
    public function show(Spj $spj)
    {
        $spj->load(['user', 'documents.uploader']);
        $documentTypes = SpjDocument::getDocumentTypes();

        // Get document completeness analysis
        $documentAnalysis = $this->analyzeDocumentCompleteness($spj);

        return view('monitoring.spj.show', compact('spj', 'documentTypes', 'documentAnalysis'));
    }

    /**
     * Get SPJ statistics for admin dashboard
     */
    private function getStatistics()
    {
        return [
            'total_spj' => Spj::count(),
            'total_anggaran' => Spj::sum('anggaran'),
            'spj_with_documents' => Spj::whereHas('documents')->count(),
            'spj_without_documents' => Spj::whereDoesntHave('documents')->count(),
            'total_documents' => SpjDocument::count(),
            'total_desa' => User::where('role', 'desa')->count(),
            'recent_spj' => Spj::with('user')->latest()->take(5)->get(),
            'top_desa_by_spj' => User::where('role', 'desa')
                ->withCount('spjs')
                ->orderBy('spjs_count', 'desc')
                ->take(5)
                ->get(),
            'monthly_spj' => Spj::select(
                DB::raw('YEAR(tanggal_kegiatan) as year'),
                DB::raw('MONTH(tanggal_kegiatan) as month'),
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(anggaran) as total_anggaran')
            )
                ->groupBy('year', 'month')
                ->orderBy('year', 'desc')
                ->orderBy('month', 'desc')
                ->take(12)
                ->get(),
        ];
    }

    /**
     * Analyze document completeness for an SPJ
     */
    private function analyzeDocumentCompleteness(Spj $spj)
    {
        $requiredDocuments = SpjDocument::getDocumentTypes();
        $uploadedDocuments = $spj->documents->pluck('document_type')->toArray();

        $analysis = [];
        foreach ($requiredDocuments as $type => $label) {
            $analysis[$type] = [
                'label' => $label,
                'uploaded' => in_array($type, $uploadedDocuments),
                'document' => $spj->documents->where('document_type', $type)->first()
            ];
        }

        $completeness = count(array_filter($analysis, fn($item) => $item['uploaded']));
        $total = count($requiredDocuments);
        $percentage = $total > 0 ? round(($completeness / $total) * 100) : 0;

        return [
            'items' => $analysis,
            'completed' => $completeness,
            'total' => $total,
            'percentage' => $percentage
        ];
    }

    /**
     * Export SPJ data to Excel
     */
    public function export(Request $request)
    {
        try {
            // Get filter parameters
            $filters = [
                'search' => $request->get('search'),
                'desa' => $request->get('desa'),
                'status' => $request->get('status'),
                'date_from' => $request->get('date_from'),
                'date_to' => $request->get('date_to'),
            ];

            // Generate filename with timestamp
            $timestamp = now()->format('Y-m-d_H-i-s');
            $filename = "SPJ_Export_{$timestamp}.xlsx";

            // Create and download the export
            return Excel::download(new \App\Exports\SpjExport($filters), $filename);
        } catch (\Exception $e) {
            // Log the error
            Log::error('SPJ Export Error: ' . $e->getMessage());

            // Return error response
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan saat mengekspor data: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()->with('error', 'Terjadi kesalahan saat mengekspor data.');
        }
    }

    /**
     * Get SPJ statistics API for charts
     */
    public function statistics()
    {
        $stats = $this->getStatistics();
        return response()->json($stats);
    }

    /**
     * Update status kelengkapan SPJ (Admin only)
     */
    public function updateStatus(Request $request, Spj $spj)
    {
        $request->validate([
            'status_kelengkapan' => 'required|in:pending,lengkap,belum_lengkap'
        ]);

        $spj->update([
            'status_kelengkapan' => $request->status_kelengkapan
        ]);

        return redirect()->back()->with('success', 'Status kelengkapan SPJ berhasil diperbarui.');
    }

    /**
     * Download document for monitoring (Admin only)
     */
    public function downloadDocument(SpjDocument $spjDocument)
    {
        try {
            $fullPath = storage_path('app/public/' . $spjDocument->file_path);

            if (file_exists($fullPath)) {
                return response()->download($fullPath, $spjDocument->original_name);
            }

            return redirect()->back()->with('error', 'File tidak ditemukan.');
        } catch (\Exception $e) {
            Log::error('Document download error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Terjadi kesalahan saat mengunduh file.');
        }
    }
}
