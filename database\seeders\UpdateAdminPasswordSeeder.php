<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UpdateAdminPasswordSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update admin password
        User::where('email', '<EMAIL>')->update([
            'password' => Hash::make('password')
        ]);

        User::where('email', '<EMAIL>')->update([
            'password' => Hash::make('password')
        ]);

        echo "Admin passwords updated successfully\n";
    }
}
