<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user
        User::create([
            'name' => 'Test User',
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create an admin user
        User::create([
            'name' => 'Admin User',
            'username' => 'admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'email_verified_at' => now(),
        ]);

        // Create desa users
        User::create([
            'name' => 'Desa Sukamaju',
            'username' => 'desa_sukamaju',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'desa',
            'nama_kecamatan' => 'Kecamatan Sukamaju',
            'nama_desa' => 'Desa Sukamaju',
            'alamat_desa' => 'Jl. Raya Sukamaju No. 123, Sukamaju',
            'nama_kepala_desa' => 'Bapak Suharto',
            'no_handphone' => '081234567890',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Desa Makmur',
            'username' => 'desa_makmur',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'desa',
            'nama_kecamatan' => 'Kecamatan Makmur',
            'nama_desa' => 'Desa Makmur',
            'alamat_desa' => 'Jl. Makmur Sentosa No. 456, Makmur',
            'nama_kepala_desa' => 'Bapak Sutrisno',
            'no_handphone' => '081234567891',
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'Desa Sejahtera Indah',
            'username' => 'desa_sejahtera',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'role' => 'desa',
            'nama_kecamatan' => 'Kecamatan Sejahtera',
            'nama_desa' => 'Desa Sejahtera Indah',
            'alamat_desa' => 'Jl. Sejahtera Raya No. 789, Sejahtera Indah',
            'nama_kepala_desa' => 'Bapak Bambang',
            'no_handphone' => '081234567892',
            'email_verified_at' => now(),
        ]);
    }
}
