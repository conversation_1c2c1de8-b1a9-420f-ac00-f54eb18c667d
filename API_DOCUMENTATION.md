# SiPanda API Documentation

## Dashboard API Endpoints

Base URL: `http://127.0.0.1:8001/api`

### 1. Simple Dashboard Statistics

**Endpoint:** `GET /dashboard/simple`

**Description:** Get basic dashboard statistics (total SPJ and SPJ lengkap only)

**Authentication:** Not required

**Response:**
```json
{
    "success": true,
    "message": "Simple dashboard statistics retrieved successfully",
    "data": {
        "total_spj": 5,
        "spj_lengkap": 1
    }
}
```

### 2. Complete Dashboard Statistics

**Endpoint:** `GET /dashboard`

**Description:** Get comprehensive dashboard statistics for admin view

**Authentication:** Not required

**Response:**
```json
{
    "success": true,
    "message": "Dashboard statistics retrieved successfully",
    "data": {
        "total_spj": 5,
        "spj_lengkap": 1,
        "spj_pending": 4,
        "spj_belum_lengkap": 0,
        "total_desa": 3,
        "total_dokumen": 2,
        "total_anggaran": "101000000.00",
        "completion_rate": 20,
        "status_breakdown": {
            "pending": {
                "count": 4,
                "percentage": 80
            },
            "lengkap": {
                "count": 1,
                "percentage": 20
            },
            "belum_lengkap": {
                "count": 0,
                "percentage": 0
            }
        }
    }
}
```

### 3. Chart Data for Pie Chart

**Endpoint:** `GET /dashboard/chart-data`

**Description:** Get data formatted for Chart.js pie chart

**Authentication:** Not required

**Response:**
```json
{
    "success": true,
    "message": "Chart data retrieved successfully",
    "data": {
        "labels": ["Pending", "Lengkap", "Belum Lengkap"],
        "datasets": [
            {
                "data": [4, 1, 0],
                "backgroundColor": ["#FBBF24", "#34D399", "#F87171"],
                "borderColor": ["#F59E0B", "#10B981", "#EF4444"],
                "borderWidth": 2
            }
        ]
    }
}
```

### 4. Desa Dashboard Statistics (Protected)

**Endpoint:** `GET /dashboard/desa`

**Description:** Get dashboard statistics for specific desa (authenticated user)

**Authentication:** Required (Bearer Token)

**Headers:**
```
Authorization: Bearer {token}
Accept: application/json
```

**Response:**
```json
{
    "success": true,
    "message": "Desa dashboard statistics retrieved successfully",
    "data": {
        "total_spj": 2,
        "spj_lengkap": 1,
        "spj_pending": 1,
        "spj_belum_lengkap": 0,
        "total_dokumen": 2,
        "total_anggaran": "51000000.00",
        "completion_rate": 50,
        "status_breakdown": {
            "pending": {
                "count": 1,
                "percentage": 50
            },
            "lengkap": {
                "count": 1,
                "percentage": 50
            },
            "belum_lengkap": {
                "count": 0,
                "percentage": 0
            }
        }
    }
}
```

### 5. Desa Chart Data (Protected)

**Endpoint:** `GET /dashboard/desa/chart-data`

**Description:** Get chart data for specific desa pie chart

**Authentication:** Required (Bearer Token)

**Headers:**
```
Authorization: Bearer {token}
Accept: application/json
```

**Response:**
```json
{
    "success": true,
    "message": "Desa chart data retrieved successfully",
    "data": {
        "labels": ["Pending", "Lengkap", "Belum Lengkap"],
        "datasets": [
            {
                "data": [1, 1, 0],
                "backgroundColor": ["#FBBF24", "#34D399", "#F87171"],
                "borderColor": ["#F59E0B", "#10B981", "#EF4444"],
                "borderWidth": 2
            }
        ]
    }
}
```

## Data Fields Description

### Dashboard Statistics Fields

| Field | Type | Description |
|-------|------|-------------|
| `total_spj` | integer | Total number of SPJ records |
| `spj_lengkap` | integer | Number of SPJ with status "lengkap" |
| `spj_pending` | integer | Number of SPJ with status "pending" |
| `spj_belum_lengkap` | integer | Number of SPJ with status "belum_lengkap" |
| `total_desa` | integer | Total number of desa users |
| `total_dokumen` | integer | Total number of uploaded documents |
| `total_anggaran` | string | Total budget amount (decimal string) |
| `completion_rate` | float | Percentage of completed SPJ (0-100) |

### Status Breakdown Fields

| Field | Type | Description |
|-------|------|-------------|
| `count` | integer | Number of SPJ with this status |
| `percentage` | float | Percentage of SPJ with this status (0-100) |

### Chart Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `labels` | array | Chart labels for each status |
| `data` | array | Data values for each status |
| `backgroundColor` | array | Background colors for chart segments |
| `borderColor` | array | Border colors for chart segments |
| `borderWidth` | integer | Border width for chart segments |

## Error Responses

### 401 Unauthorized (for protected endpoints)
```json
{
    "message": "Unauthenticated."
}
```

### 500 Internal Server Error
```json
{
    "success": false,
    "message": "Internal server error",
    "error": "Error details"
}
```

## Usage Examples

### Using cURL

```bash
# Get simple statistics
curl -X GET "http://127.0.0.1:8000/api/dashboard/simple" \
     -H "Accept: application/json"

# Get complete statistics
curl -X GET "http://127.0.0.1:8000/api/dashboard" \
     -H "Accept: application/json"

# Get chart data
curl -X GET "http://127.0.0.1:8000/api/dashboard/chart-data" \
     -H "Accept: application/json"

# Get desa statistics (with authentication)
curl -X GET "http://127.0.0.1:8000/api/dashboard/desa" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Using JavaScript (Fetch API)

```javascript
// Get simple statistics
fetch('http://127.0.0.1:8000/api/dashboard/simple')
    .then(response => response.json())
    .then(data => {
        console.log('Total SPJ:', data.data.total_spj);
        console.log('SPJ Lengkap:', data.data.spj_lengkap);
    });

// Get chart data for Chart.js
fetch('http://127.0.0.1:8000/api/dashboard/chart-data')
    .then(response => response.json())
    .then(data => {
        const chartData = data.data;
        // Use chartData directly with Chart.js
        new Chart(ctx, {
            type: 'pie',
            data: chartData
        });
    });
```

---

## Monitoring SPJ API Endpoints

### 1. Get All SPJ with Filtering and Pagination

**Endpoint:** `GET /monitoring/spj`

**Description:** Get list of all SPJ with filtering, searching, and pagination

**Authentication:** Not required

**Query Parameters:**
- `status` (optional): Filter by status (`pending`, `lengkap`, `belum_lengkap`)
- `desa_id` (optional): Filter by specific desa ID
- `search` (optional): Search by nama kegiatan
- `sort_by` (optional): Sort field (default: `created_at`)
- `sort_order` (optional): Sort order (`asc`, `desc`, default: `desc`)
- `per_page` (optional): Items per page (default: 15)
- `page` (optional): Page number (default: 1)

**Example Request:**
```
GET /api/monitoring/spj?status=pending&desa_id=3&search=jalan&per_page=10&page=1
```

### 2. Get SPJ Details

**Endpoint:** `GET /monitoring/spj/{id}`

**Description:** Get detailed information of specific SPJ including documents

**Authentication:** Not required

### 3. Update SPJ Status (Protected - Admin Only)

**Endpoint:** `PATCH /monitoring/spj/{id}/status`

**Description:** Update SPJ status (admin only)

**Authentication:** Required (Bearer Token + Admin Role)

**Request Body:**
```json
{
    "status_kelengkapan": "lengkap"
}
```

### 4. Get SPJ Documents

**Endpoint:** `GET /monitoring/spj/{id}/documents`

**Description:** Get all documents for specific SPJ

**Authentication:** Not required

### 5. Get Monitoring Statistics

**Endpoint:** `GET /monitoring/spj/statistics`

**Description:** Get comprehensive monitoring statistics

**Authentication:** Not required

### 6. Get Desa List

**Endpoint:** `GET /monitoring/spj/desa-list`

**Description:** Get list of all desa for filtering purposes

**Authentication:** Not required
