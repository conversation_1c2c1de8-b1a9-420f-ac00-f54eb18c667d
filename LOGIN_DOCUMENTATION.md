# SiPanda Login Page Documentation

## Overview
Halaman login Si<PERSON>anda telah dibuat dengan desain yang modern dan responsif berdasarkan referensi yang diberikan. Halaman ini menggunakan Laravel dengan Tailwind CSS untuk styling.

## Features
- **Responsive Design**: <PERSON>aman dapat diakses dengan baik di desktop dan mobile
- **Tab Navigation**: Switching antara Login dan Sign Up
- **Form Validation**: Validasi username dan password dengan error messages
- **Password Toggle**: Tombol untuk show/hide password
- **Social Login Buttons**: Tombol untuk login dengan Google, Apple, Binance, dan Wallet (UI only)
- **Modern UI**: Menggunakan Tailwind CSS dengan desain yang clean dan modern

## File Structure
```
resources/views/auth/
├── login.blade.php      # Halaman login
└── register.blade.php   # Halaman register

app/Http/Controllers/
└── AuthController.php   # Controller untuk authentication

routes/
└── web.php             # Routes untuk login/register
```

## Routes
- `GET /login` - Menampilkan halaman login
- `POST /login` - Proses login
- `GET /register` - Menampilkan halaman register
- `POST /register` - Proses registrasi
- `POST /logout` - Logout user
- `GET /dashboard` - Dashboard setelah login (protected)

## Test Accounts
Untuk testing, sudah dibuat 2 user account:

1. **Test User**
   - Username: `testuser`
   - Password: `password123`

2. **Admin User**
   - Username: `admin`
   - Password: `admin123`

## How to Use

### 1. Setup Development Environment
```bash
# Install dependencies
npm install

# Run migrations and seeders
php artisan migrate:fresh --seed

# Start Laravel server
php artisan serve

# Start Vite (in another terminal)
npm run dev
```

### 2. Access the Login Page
- Open browser and go to: `http://127.0.0.1:8000/login`
- Use one of the test accounts above to login
- After successful login, you'll be redirected to `/dashboard`

### 3. Register New Account
- Click "Sign Up" tab or link
- Fill in the registration form
- After successful registration, you'll be automatically logged in

## Customization

### Styling
- Main CSS file: `resources/css/app.css`
- Uses Tailwind CSS v4.0
- Custom font: Instrument Sans

### Authentication Logic
- Controller: `app/Http/Controllers/AuthController.php`
- Uses Laravel's built-in authentication
- Password hashing with bcrypt
- Session-based authentication

### Social Login Integration
Currently, social login buttons are UI-only. To implement actual social login:

1. Install Laravel Socialite
2. Configure OAuth providers in `config/services.php`
3. Add routes and controller methods for each provider
4. Update the button links to point to social login routes

## Security Features
- CSRF protection on all forms
- Password confirmation for registration
- Username validation
- Session regeneration on login
- Secure password hashing

## Browser Compatibility
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile responsive design
- Supports touch interactions

## Next Steps
1. Implement actual social login functionality
2. Add forgot password feature
3. Add email verification
4. Implement remember me functionality
5. Add rate limiting for login attempts
