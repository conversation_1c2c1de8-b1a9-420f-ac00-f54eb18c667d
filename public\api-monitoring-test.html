<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiPanda Monitoring SPJ API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">SiPanda Monitoring SPJ API Test</h1>
        
        <!-- Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Monitoring Statistics</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total SPJ</p>
                    <p class="text-2xl font-bold text-blue-600" id="stats-total-spj">-</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Pending</p>
                    <p class="text-2xl font-bold text-yellow-600" id="stats-spj-pending">-</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Lengkap</p>
                    <p class="text-2xl font-bold text-green-600" id="stats-spj-lengkap">-</p>
                </div>
                <div class="bg-red-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Belum Lengkap</p>
                    <p class="text-2xl font-bold text-red-600" id="stats-spj-belum-lengkap">-</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-purple-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total Desa</p>
                    <p class="text-2xl font-bold text-purple-600" id="stats-total-desa">-</p>
                </div>
                <div class="bg-indigo-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total Dokumen</p>
                    <p class="text-2xl font-bold text-indigo-600" id="stats-total-dokumen">-</p>
                </div>
                <div class="bg-pink-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Completion Rate</p>
                    <p class="text-2xl font-bold text-pink-600" id="stats-completion-rate">-</p>
                </div>
            </div>
            <button onclick="loadStatistics()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Load Statistics
            </button>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">SPJ List with Filters</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="filter-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Status</option>
                        <option value="pending">Pending</option>
                        <option value="lengkap">Lengkap</option>
                        <option value="belum_lengkap">Belum Lengkap</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Desa</label>
                    <select id="filter-desa" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">All Desa</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                    <input type="text" id="filter-search" placeholder="Search kegiatan..." 
                           class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div class="flex items-end">
                    <button onclick="loadSpjList()" class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Apply Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- SPJ List -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">SPJ List</h2>
            <div id="spj-list" class="space-y-4">
                <p class="text-gray-500">Click "Apply Filters" to load SPJ list...</p>
            </div>
            <div id="pagination" class="mt-4 flex justify-center">
                <!-- Pagination will be inserted here -->
            </div>
        </div>

        <!-- SPJ Details -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">SPJ Details</h2>
            <div class="mb-4">
                <input type="number" id="spj-id" placeholder="Enter SPJ ID" 
                       class="border border-gray-300 rounded-md px-3 py-2 mr-2">
                <button onclick="loadSpjDetails()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    Load Details
                </button>
            </div>
            <div id="spj-details">
                <p class="text-gray-500">Enter SPJ ID and click "Load Details" to see SPJ information...</p>
            </div>
        </div>

        <!-- API Response -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">API Response</h2>
            <pre id="api-response" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
Click any button above to see API response here...
            </pre>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';

        function displayResponse(data) {
            document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
        }

        async function loadStatistics() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/spj/statistics`);
                const data = await response.json();
                
                const overview = data.data.overview;
                document.getElementById('stats-total-spj').textContent = overview.total_spj;
                document.getElementById('stats-spj-pending').textContent = overview.spj_pending;
                document.getElementById('stats-spj-lengkap').textContent = overview.spj_lengkap;
                document.getElementById('stats-spj-belum-lengkap').textContent = overview.spj_belum_lengkap;
                document.getElementById('stats-total-desa').textContent = overview.total_desa;
                document.getElementById('stats-total-dokumen').textContent = overview.total_dokumen;
                document.getElementById('stats-completion-rate').textContent = overview.completion_rate + '%';
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading statistics:', error);
                displayResponse({ error: error.message });
            }
        }

        async function loadDesaList() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/spj/desa-list`);
                const data = await response.json();
                
                const select = document.getElementById('filter-desa');
                select.innerHTML = '<option value="">All Desa</option>';
                
                data.data.forEach(desa => {
                    const option = document.createElement('option');
                    option.value = desa.id;
                    option.textContent = `${desa.name} (${desa.spj_count} SPJ)`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Error loading desa list:', error);
            }
        }

        async function loadSpjList() {
            try {
                const status = document.getElementById('filter-status').value;
                const desaId = document.getElementById('filter-desa').value;
                const search = document.getElementById('filter-search').value;
                
                const params = new URLSearchParams();
                if (status) params.append('status', status);
                if (desaId) params.append('desa_id', desaId);
                if (search) params.append('search', search);
                
                const response = await fetch(`${API_BASE}/monitoring/spj?${params}`);
                const data = await response.json();
                
                const listContainer = document.getElementById('spj-list');
                listContainer.innerHTML = '';
                
                if (data.data.length === 0) {
                    listContainer.innerHTML = '<p class="text-gray-500">No SPJ found with current filters.</p>';
                } else {
                    data.data.forEach(spj => {
                        const spjCard = document.createElement('div');
                        spjCard.className = 'border border-gray-200 rounded-lg p-4';
                        spjCard.innerHTML = `
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-semibold text-gray-900">${spj.nama_kegiatan}</h3>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${spj.status_badge_class}">
                                    ${spj.formatted_status}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">Desa: ${spj.desa.name}</p>
                            <p class="text-sm text-gray-600 mb-2">Anggaran: ${spj.formatted_anggaran}</p>
                            <p class="text-sm text-gray-600 mb-2">Dokumen: ${spj.documents_count} files</p>
                            <p class="text-xs text-gray-500">Created: ${new Date(spj.created_at).toLocaleDateString()}</p>
                        `;
                        listContainer.appendChild(spjCard);
                    });
                }
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading SPJ list:', error);
                displayResponse({ error: error.message });
            }
        }

        async function loadSpjDetails() {
            try {
                const spjId = document.getElementById('spj-id').value;
                if (!spjId) {
                    alert('Please enter SPJ ID');
                    return;
                }
                
                const response = await fetch(`${API_BASE}/monitoring/spj/${spjId}`);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.message || 'Failed to load SPJ details');
                }
                
                const spj = data.data;
                const detailsContainer = document.getElementById('spj-details');
                
                detailsContainer.innerHTML = `
                    <div class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">SPJ Information</h3>
                                <p><strong>Nama Kegiatan:</strong> ${spj.nama_kegiatan}</p>
                                <p><strong>Tanggal:</strong> ${new Date(spj.tanggal_kegiatan).toLocaleDateString()}</p>
                                <p><strong>Anggaran:</strong> ${spj.formatted_anggaran}</p>
                                <p><strong>Status:</strong> <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${spj.status_badge_class}">${spj.formatted_status}</span></p>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Desa Information</h3>
                                <p><strong>Nama Desa:</strong> ${spj.desa.name}</p>
                                <p><strong>Email:</strong> ${spj.desa.email}</p>
                                <p><strong>Role:</strong> ${spj.desa.role}</p>
                            </div>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Documents (${spj.documents_count})</h3>
                            ${spj.documents.length > 0 ? 
                                spj.documents.map(doc => `
                                    <div class="border border-gray-200 rounded p-3 mb-2">
                                        <p><strong>Type:</strong> ${doc.document_type}</p>
                                        <p><strong>File:</strong> ${doc.file_name}</p>
                                        <p><strong>Size:</strong> ${doc.formatted_file_size}</p>
                                        <p><strong>Uploaded by:</strong> ${doc.uploaded_by.name}</p>
                                        <p><strong>Uploaded at:</strong> ${new Date(doc.uploaded_at).toLocaleString()}</p>
                                    </div>
                                `).join('') 
                                : '<p class="text-gray-500">No documents uploaded</p>'
                            }
                        </div>
                        <div>
                            <p><strong>Keterangan:</strong> ${spj.keterangan}</p>
                        </div>
                    </div>
                `;
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading SPJ details:', error);
                displayResponse({ error: error.message });
                document.getElementById('spj-details').innerHTML = `<p class="text-red-500">Error: ${error.message}</p>`;
            }
        }

        // Load initial data
        window.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadDesaList();
        });
    </script>
</body>
</html>
