<?php

namespace App\Exports;

use App\Models\Spj;
use Maat<PERSON>bsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

class SpjExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithColumnWidths, WithTitle
{
    protected $filters;

    public function __construct($filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Spj::with(['user', 'documents']);

        // Apply filters
        if (!empty($this->filters['search'])) {
            $query->where('nama_kegiatan', 'like', '%' . $this->filters['search'] . '%');
        }

        if (!empty($this->filters['desa'])) {
            $query->where('user_id', $this->filters['desa']);
        }

        if (!empty($this->filters['status'])) {
            $query->where('status_kelengkapan', $this->filters['status']);
        }

        if (!empty($this->filters['date_from'])) {
            $query->where('tanggal_kegiatan', '>=', $this->filters['date_from']);
        }

        if (!empty($this->filters['date_to'])) {
            $query->where('tanggal_kegiatan', '<=', $this->filters['date_to']);
        }

        return $query->orderBy('tanggal_kegiatan', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'No',
            'Nama Kegiatan',
            'Desa',
            'Kecamatan',
            'Nama Kepala Desa',
            'Tanggal Kegiatan',
            'Anggaran (Rp)',
            'Status Kelengkapan',
            'Jumlah Dokumen',
            'Keterangan',
            'Tanggal Dibuat',
            'Terakhir Diperbarui'
        ];
    }

    /**
     * @param mixed $spj
     * @return array
     */
    public function map($spj): array
    {
        static $no = 1;
        
        return [
            $no++,
            $spj->nama_kegiatan,
            $spj->user->nama_desa ?? $spj->user->name,
            $spj->user->nama_kecamatan ?? '-',
            $spj->user->nama_kepala_desa ?? '-',
            $spj->tanggal_kegiatan->format('d/m/Y'),
            $spj->anggaran,
            $spj->formatted_status,
            $spj->documents->count(),
            $spj->keterangan ?? '-',
            $spj->created_at->format('d/m/Y H:i'),
            $spj->updated_at->format('d/m/Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        $lastRow = $sheet->getHighestRow();
        $lastColumn = $sheet->getHighestColumn();

        return [
            // Header row styling
            1 => [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF'],
                    'size' => 12
                ],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '2563EB']
                ],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                    'vertical' => Alignment::VERTICAL_CENTER
                ]
            ],
            
            // All cells border
            "A1:{$lastColumn}{$lastRow}" => [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => '000000']
                    ]
                ]
            ],

            // Data rows alignment
            "A2:{$lastColumn}{$lastRow}" => [
                'alignment' => [
                    'vertical' => Alignment::VERTICAL_CENTER,
                    'wrapText' => true
                ]
            ],

            // Number columns alignment (No, Anggaran, Jumlah Dokumen)
            "A2:A{$lastRow}" => [
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            "G2:G{$lastRow}" => [
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_RIGHT],
                'numberFormat' => [
                    'formatCode' => '#,##0'
                ]
            ],
            "I2:I{$lastRow}" => [
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ]
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 5,   // No
            'B' => 25,  // Nama Kegiatan
            'C' => 20,  // Desa
            'D' => 15,  // Kecamatan
            'E' => 20,  // Nama Kepala Desa
            'F' => 12,  // Tanggal Kegiatan
            'G' => 15,  // Anggaran
            'H' => 15,  // Status Kelengkapan
            'I' => 10,  // Jumlah Dokumen
            'J' => 30,  // Keterangan
            'K' => 15,  // Tanggal Dibuat
            'L' => 15   // Terakhir Diperbarui
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Data SPJ - ' . date('d-m-Y');
    }
}
