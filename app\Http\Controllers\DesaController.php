<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;

class DesaController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $desas = User::desa()->latest()->paginate(10);
        return view('desa.index', compact('desas'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('desa.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users',
            'password' => 'required|string|min:6',
            'nama_kecamatan' => 'required|string|max:255',
            'nama_desa' => 'required|string|max:255',
            'alamat_desa' => 'required|string',
            'nama_kepala_desa' => 'required|string|max:255',
            'no_handphone' => 'required|string|max:20',
        ]);

        $data = $request->all();
        $data['role'] = 'desa';
        $data['email'] = $request->username . '@desa.local'; // Generate email from username

        User::create($data);

        return redirect()->route('desa.index')
            ->with('success', 'Data desa berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $desa)
    {
        return view('desa.show', compact('desa'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $desa)
    {
        return view('desa.edit', compact('desa'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $desa)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:users,username,' . $desa->id,
            'nama_kecamatan' => 'required|string|max:255',
            'nama_desa' => 'required|string|max:255',
            'alamat_desa' => 'required|string',
            'nama_kepala_desa' => 'required|string|max:255',
            'no_handphone' => 'required|string|max:20',
        ]);

        $data = $request->all();

        // Only update password if provided
        if ($request->filled('password')) {
            $request->validate(['password' => 'string|min:6']);
        } else {
            unset($data['password']);
        }

        // Update email if username changed
        if ($request->username !== $desa->username) {
            $data['email'] = $request->username . '@desa.local';
        }

        $desa->update($data);

        return redirect()->route('desa.index')
            ->with('success', 'Data desa berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $desa)
    {
        $desa->delete();

        return redirect()->route('desa.index')
            ->with('success', 'Data desa berhasil dihapus.');
    }
}
