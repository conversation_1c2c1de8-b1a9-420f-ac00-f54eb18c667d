@extends('layouts.app')

@section('title', 'Detail Monitoring SPJ - SiPanda')

@section('breadcrumb')
    <span>SiPanda</span>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <a href="{{ route('monitoring.spj.index') }}" class="text-gray-500 hover:text-gray-700">Monitoring SPJ</a>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <span class="text-gray-900 font-medium">Detail SPJ</span>
@endsection

@section('content')
    <div class="max-w-7xl mx-auto">
        <!-- Page Header -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Detail Monitoring SPJ</h1>
                    <p class="text-gray-600 mt-1">Analisis kelengkapan dan detail SPJ dari desa.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('monitoring.spj.index') }}"
                        class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center space-x-2">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        <span>Kembali</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- SPJ Information -->
            <div class="lg:col-span-2">
                <!-- Basic Info -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi SPJ</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Nama Kegiatan</label>
                            <p class="text-gray-900 font-medium">{{ $spj->nama_kegiatan }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Kegiatan</label>
                            <p class="text-gray-900">{{ $spj->tanggal_kegiatan->format('d F Y') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Anggaran</label>
                            <p class="text-gray-900 font-medium text-lg">{{ $spj->formatted_anggaran }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Dibuat Oleh</label>
                            <p class="text-gray-900">{{ $spj->user->name }}</p>
                            <p class="text-sm text-gray-500">{{ $spj->user->email }}</p>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Keterangan</label>
                            <p class="text-gray-900">{{ $spj->keterangan ?: 'Tidak ada keterangan' }}</p>
                        </div>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Dibuat Pada</label>
                            <p class="text-gray-900">{{ $spj->created_at->format('d F Y H:i') }}</p>
                        </div>
                    </div>
                </div>

                <!-- Document Completeness Analysis -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Analisis Kelengkapan Dokumen</h3>
                        <div class="flex items-center space-x-2">
                            <div class="text-right">
                                <p class="text-sm text-gray-500">Kelengkapan</p>
                                <p
                                    class="text-lg font-bold {{ $documentAnalysis['percentage'] >= 80 ? 'text-green-600' : ($documentAnalysis['percentage'] >= 50 ? 'text-yellow-600' : 'text-red-600') }}">
                                    {{ $documentAnalysis['percentage'] }}%
                                </p>
                            </div>
                            <div class="w-16 h-16 relative">
                                <svg class="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                                    <path class="text-gray-200" stroke="currentColor" stroke-width="3" fill="none"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                                    <path
                                        class="{{ $documentAnalysis['percentage'] >= 80 ? 'text-green-500' : ($documentAnalysis['percentage'] >= 50 ? 'text-yellow-500' : 'text-red-500') }}"
                                        stroke="currentColor" stroke-width="3" fill="none" stroke-linecap="round"
                                        stroke-dasharray="{{ $documentAnalysis['percentage'] }}, 100"
                                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831" />
                                </svg>
                            </div>
                        </div>
                    </div>

                    <div class="space-y-3">
                        @foreach ($documentAnalysis['items'] as $type => $item)
                            <div
                                class="flex items-center justify-between p-3 rounded-lg {{ $item['uploaded'] ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200' }}">
                                <div class="flex items-center space-x-3">
                                    <div class="flex-shrink-0">
                                        @if ($item['uploaded'])
                                            <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        @else
                                            <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z">
                                                </path>
                                            </svg>
                                        @endif
                                    </div>
                                    <div>
                                        <p class="font-medium {{ $item['uploaded'] ? 'text-green-900' : 'text-red-900' }}">
                                            {{ $item['label'] }}
                                        </p>
                                        @if ($item['uploaded'] && $item['document'])
                                            <p class="text-sm {{ $item['uploaded'] ? 'text-green-600' : 'text-red-600' }}">
                                                Diupload: {{ $item['document']->created_at->format('d/m/Y H:i') }}
                                            </p>
                                        @else
                                            <p class="text-sm {{ $item['uploaded'] ? 'text-green-600' : 'text-red-600' }}">
                                                Belum diupload
                                            </p>
                                        @endif
                                    </div>
                                </div>
                                @if ($item['uploaded'] && $item['document'])
                                    <div class="flex items-center space-x-2">
                                        <span class="text-xs {{ $item['uploaded'] ? 'text-green-600' : 'text-red-600' }}">
                                            {{ number_format($item['document']->file_size / 1024, 1) }} KB
                                        </span>
                                        <a href="{{ route('monitoring.spj.download-document', $item['document']) }}"
                                            class="text-blue-600 hover:text-blue-800 text-sm">
                                            Download
                                        </a>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>

                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-gray-600">Progress Kelengkapan:</span>
                            <span class="font-medium text-gray-900">
                                {{ $documentAnalysis['completed'] }} dari {{ $documentAnalysis['total'] }} dokumen
                            </span>
                        </div>
                        <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                            <div class="h-2 rounded-full {{ $documentAnalysis['percentage'] >= 80 ? 'bg-green-500' : ($documentAnalysis['percentage'] >= 50 ? 'bg-yellow-500' : 'bg-red-500') }}"
                                style="width: {{ $documentAnalysis['percentage'] }}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar Info -->
            <div class="space-y-6">
                <!-- Status Summary -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Status Summary</h3>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Total Dokumen</span>
                            <span class="font-medium text-gray-900">{{ $spj->documents->count() }}</span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Kelengkapan</span>
                            <span
                                class="font-medium {{ $documentAnalysis['percentage'] >= 80 ? 'text-green-600' : ($documentAnalysis['percentage'] >= 50 ? 'text-yellow-600' : 'text-red-600') }}">
                                {{ $documentAnalysis['percentage'] }}%
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Status Kelengkapan</span>
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $spj->status_badge_class }}">
                                {{ $spj->formatted_status }}
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-600">Terakhir Update</span>
                            <span class="text-sm text-gray-900">
                                {{ $spj->updated_at->format('d/m/Y') }}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Update Status (Admin Only) -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Update Status</h3>
                    <form action="{{ route('monitoring.spj.update-status', $spj) }}" method="POST">
                        @csrf
                        @method('PATCH')

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status Kelengkapan</label>
                                <select name="status_kelengkapan"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500">
                                    @foreach (\App\Models\Spj::getStatusOptions() as $value => $label)
                                        <option value="{{ $value }}"
                                            {{ $spj->status_kelengkapan === $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <button type="submit"
                                class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center justify-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                                </svg>
                                <span>Update Status</span>
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Desa Information -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Informasi Desa</h3>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Nama Desa</label>
                            <p class="text-gray-900">{{ $spj->user->name }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="text-gray-900">{{ $spj->user->email }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total SPJ</label>
                            <p class="text-gray-900">{{ $spj->user->spjs->count() }} SPJ</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Total Anggaran</label>
                            <p class="text-gray-900">Rp
                                {{ number_format($spj->user->spjs->sum('anggaran'), 0, ',', '.') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


@endsection
