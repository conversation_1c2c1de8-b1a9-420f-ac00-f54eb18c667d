<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\DesaController;
use App\Http\Controllers\SpjController;
use App\Http\Controllers\MonitoringSpjController;
use App\Models\Spj;
use Illuminate\Http\Request;

Route::get('/', function () {
    return view('welcome');
});

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');



// Protected Routes
Route::middleware('auth')->group(function () {
    // Dashboard - accessible by all authenticated users
    Route::get('/dashboard', function () {
        return view('dashboard');
    })->name('dashboard');
});

// Admin Only Routes
Route::middleware(['auth', 'admin'])->group(function () {
    // Desa Management Routes - Only Admin
    Route::resource('desa', DesaController::class);

    // SPJ Monitoring Routes - Only Admin
    Route::get('monitoring/spj', [MonitoringSpjController::class, 'index'])->name('monitoring.spj.index');
    Route::get('monitoring/spj/export', [MonitoringSpjController::class, 'export'])->name('monitoring.spj.export');
    Route::get('monitoring/spj/statistics', [MonitoringSpjController::class, 'statistics'])->name('monitoring.spj.statistics');
    Route::get('monitoring/spj/document/{spjDocument}/download', [MonitoringSpjController::class, 'downloadDocument'])->name('monitoring.spj.download-document');
    Route::get('monitoring/spj/{spj}', [MonitoringSpjController::class, 'show'])->name('monitoring.spj.show');
    Route::patch('monitoring/spj/{spj}/status', [MonitoringSpjController::class, 'updateStatus'])->name('monitoring.spj.update-status');
});

// Desa Only Routes
Route::middleware(['auth', 'desa'])->group(function () {
    // SPJ Management Routes - Only Desa
    Route::resource('spj', SpjController::class);

    // SPJ Document Routes - Only Desa
    Route::post('spj/{spj}/upload-document', [SpjController::class, 'uploadDocument'])->name('spj.upload-document');
    Route::get('spj-document/{spjDocument}/download', [SpjController::class, 'downloadDocument'])->name('spj.download-document');
    Route::delete('spj-document/{spjDocument}', [SpjController::class, 'deleteDocument'])->name('spj.delete-document');
});
