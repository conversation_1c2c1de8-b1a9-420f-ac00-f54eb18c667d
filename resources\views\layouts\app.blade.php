<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'SiPanda')</title>
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Instrument+Sans:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap"
        rel="stylesheet">

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    @stack('styles')
</head>

<body class="bg-gray-50 font-sans">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
            <!-- Company Header -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gray-900 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5" />
                        </svg>
                    </div>
                    <span class="font-semibold text-gray-900">SiPanda</span>
                </div>
            </div>



            <!-- Navigation Menu -->
            <div class="flex-1 overflow-y-auto">
                <!-- Menu Utama -->
                <div class="p-4">
                    <div class="text-xs font-medium text-gray-500 uppercase tracking-wider mb-3">Menu Utama</div>
                    <nav class="space-y-1">
                        <!-- Dashboard -->
                        <a href="{{ route('dashboard') }}"
                            class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('dashboard') ? 'text-gray-900 bg-gray-100' : 'text-gray-700 hover:bg-gray-100' }} rounded-lg group">
                            <svg class="w-5 h-5 mr-3 {{ request()->routeIs('dashboard') ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500' }}"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                </path>
                            </svg>
                            Dashboard
                        </a>

                        @if (auth()->user()->isAdmin())
                            <!-- Manajemen Desa - Admin Only -->
                            <a href="{{ route('desa.index') }}"
                                class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('desa.*') ? 'text-gray-900 bg-gray-100' : 'text-gray-700 hover:bg-gray-100' }} rounded-lg group">
                                <svg class="w-5 h-5 mr-3 {{ request()->routeIs('desa.*') ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500' }}"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4">
                                    </path>
                                </svg>
                                Manajemen Desa
                            </a>

                            <!-- Monitoring SPJ - Admin Only -->
                            <a href="{{ route('monitoring.spj.index') }}"
                                class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('monitoring.spj.*') ? 'text-gray-900 bg-gray-100' : 'text-gray-700 hover:bg-gray-100' }} rounded-lg group">
                                <svg class="w-5 h-5 mr-3 {{ request()->routeIs('monitoring.spj.*') ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500' }}"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                                    </path>
                                </svg>
                                Monitoring SPJ
                            </a>
                        @endif

                        @if (auth()->user()->isDesa())
                            <!-- Kelengkapan SPJ - Desa Only -->
                            <a href="{{ route('spj.index') }}"
                                class="flex items-center px-3 py-2 text-sm font-medium {{ request()->routeIs('spj.*') ? 'text-gray-900 bg-gray-100' : 'text-gray-700 hover:bg-gray-100' }} rounded-lg group">
                                <svg class="w-5 h-5 mr-3 {{ request()->routeIs('spj.*') ? 'text-gray-500' : 'text-gray-400 group-hover:text-gray-500' }}"
                                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                                    </path>
                                </svg>
                                Kelengkapan SPJ
                            </a>
                        @endif
                    </nav>
                </div>


            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top Header -->
            <header class="bg-white border-b border-gray-200 px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <!-- Breadcrumb -->
                        <div class="flex items-center space-x-2 text-sm text-gray-500">
                            @yield('breadcrumb')
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-700">Welcome, {{ Auth::user()->username }}</span>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="text-sm text-gray-500 hover:text-gray-700">
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto bg-gray-50 p-6">
                @yield('content')
            </main>
        </div>
    </div>

    @stack('scripts')
</body>

</html>
