@extends('layouts.app')

@section('title', 'Detail SPJ - SiPanda')

@section('breadcrumb')
    <span>SiPanda</span>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <a href="{{ route('spj.index') }}" class="text-gray-500 hover:text-gray-700">Kelengkapan SPJ</a>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <span class="text-gray-900 font-medium">Detail SPJ</span>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <!-- Page Title -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Detail SPJ</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap Surat Pertanggungjawaban.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('spj.index') }}"
                        class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        <span>Kembali</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Detail Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Informasi SPJ</h3>
            </div>

            <!-- Content -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nama Kegiatan -->
                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-500 mb-1">Nama Kegiatan</label>
                        <p class="text-gray-900 font-medium">{{ $spj->nama_kegiatan }}</p>
                    </div>

                    <!-- Tanggal Kegiatan -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Tanggal Kegiatan</label>
                        <p class="text-gray-900 font-medium">{{ $spj->tanggal_kegiatan->format('d F Y') }}</p>
                    </div>

                    <!-- Anggaran -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Anggaran</label>
                        <p class="text-gray-900 font-medium text-lg text-green-600">{{ $spj->formatted_anggaran }}</p>
                    </div>

                    <!-- Dibuat Oleh -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Dibuat Oleh</label>
                        <p class="text-gray-900 font-medium">{{ $spj->user->name }}</p>
                    </div>

                    <!-- Tanggal Dibuat -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Tanggal Dibuat</label>
                        <p class="text-gray-900 font-medium">{{ $spj->created_at->format('d F Y H:i') }}</p>
                    </div>

                    <!-- Status Kelengkapan -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Status Kelengkapan</label>
                        <span
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $spj->status_badge_class }}">
                            {{ $spj->formatted_status }}
                        </span>
                    </div>

                    <!-- Keterangan -->
                    @if ($spj->keterangan)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-500 mb-1">Keterangan</label>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <p class="text-gray-900">{{ $spj->keterangan }}</p>
                            </div>
                        </div>
                    @endif

                    <!-- Terakhir Diupdate -->
                    @if ($spj->updated_at != $spj->created_at)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-500 mb-1">Terakhir Diupdate</label>
                            <p class="text-gray-900 font-medium">{{ $spj->updated_at->format('d F Y H:i') }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-end space-x-3">
                <a href="{{ route('spj.edit', $spj) }}"
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                        </path>
                    </svg>
                    <span>Edit SPJ</span>
                </a>
                <form action="{{ route('spj.destroy', $spj) }}" method="POST" class="inline"
                    onsubmit="return confirm('Apakah Anda yakin ingin menghapus data SPJ ini? Tindakan ini tidak dapat dibatalkan.')">
                    @csrf
                    @method('DELETE')
                    <button type="submit"
                        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                            </path>
                        </svg>
                        <span>Hapus SPJ</span>
                    </button>
                </form>
            </div>
        </div>

        <!-- Upload Documents Section -->
        @include('spj.partials.upload-form')

        <!-- Documents List Section -->
        @include('spj.partials.documents-list')
    </div>
@endsection
