<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SpjDocument extends Model
{
    protected $fillable = [
        'spj_id',
        'document_type',
        'document_name',
        'original_name',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'uploaded_by',
    ];

    /**
     * Document type labels
     */
    public static function getDocumentTypes(): array
    {
        return [
            'spp' => 'SPP',
            'sptjb' => 'SPTJB',
            'kwitansi_siskeudes' => 'Kwitansi Siskeudes',
            'tanda_terima' => 'Tanda Terima',
            'photo' => 'Photo',
            'nota' => 'Nota (Pembelian Barang)',
            'pembayaran_pajak' => 'Pembayaran Pajak',
            'notulen_berita_acara' => 'Notulen atau Berita Acara',
            'absensi' => 'Absensi',
            'lainnya' => 'Kelengkapan Lai<PERSON>',
        ];
    }

    /**
     * Get document type label
     */
    public function getDocumentTypeLabelAttribute(): string
    {
        $types = self::getDocumentTypes();
        return $types[$this->document_type] ?? $this->document_type;
    }

    /**
     * Get display name for document
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->document_type === 'lainnya' && $this->document_name) {
            return $this->document_name;
        }
        return $this->document_type_label;
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        $bytes = $this->file_size;
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        }
        return $bytes . ' bytes';
    }

    /**
     * Get the SPJ that owns the document.
     */
    public function spj(): BelongsTo
    {
        return $this->belongsTo(Spj::class);
    }

    /**
     * Get the user who uploaded the document.
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
