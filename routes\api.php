<?php

use App\Http\Controllers\Api\DashboardController;
use App\Http\Controllers\Api\MonitoringSpjController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Authentication endpoints for API testing
Route::post('/auth/login', function (Request $request) {
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    $user = \App\Models\User::where('email', $request->email)->first();

    if (!$user || !\Illuminate\Support\Facades\Hash::check($request->password, $user->password)) {
        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 401);
    }

    $token = $user->createToken('api-token')->plainTextToken;

    return response()->json([
        'success' => true,
        'message' => 'Login successful',
        'data' => [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'role' => $user->role,
            ],
            'token' => $token,
            'token_type' => 'Bearer'
        ]
    ]);
});

Route::middleware('auth:sanctum')->post('/auth/logout', function (Request $request) {
    $request->user()->currentAccessToken()->delete();

    return response()->json([
        'success' => true,
        'message' => 'Logout successful'
    ]);
});

// Dashboard API Routes
Route::prefix('dashboard')->group(function () {
    // Public dashboard statistics (no auth required)
    Route::get('/', [DashboardController::class, 'index']);
    Route::get('/simple', [DashboardController::class, 'simple']);
    Route::get('/chart-data', [DashboardController::class, 'chartData']);

    // Protected routes (require authentication)
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('/desa', [DashboardController::class, 'desa']);
        Route::get('/desa/chart-data', [DashboardController::class, 'desaChartData']);
    });
});

// Monitoring SPJ API Routes
Route::prefix('monitoring/spj')->group(function () {
    // Public monitoring endpoints
    Route::get('/', [MonitoringSpjController::class, 'index']);
    Route::get('/statistics', [MonitoringSpjController::class, 'statistics']);
    Route::get('/desa-list', [MonitoringSpjController::class, 'desaList']);
    Route::get('/{spj}', [MonitoringSpjController::class, 'show']);
    Route::get('/{spj}/documents', [MonitoringSpjController::class, 'documents']);

    // Protected endpoints (admin only)
    Route::middleware(['auth:sanctum', 'api.admin'])->group(function () {
        Route::patch('/{spj}/status', [MonitoringSpjController::class, 'updateStatus']);
        Route::get('/export', [\App\Http\Controllers\MonitoringSpjController::class, 'export']);
    });
});
