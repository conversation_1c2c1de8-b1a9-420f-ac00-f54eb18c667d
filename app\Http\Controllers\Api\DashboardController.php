<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Spj;
use App\Models\User;
use App\Models\SpjDocument;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics for all users
     */
    public function index(): JsonResponse
    {
        $totalSpj = Spj::count();
        $spjLengkap = Spj::where('status_kelengkapan', 'lengkap')->count();
        $spjPending = Spj::where('status_kelengkapan', 'pending')->count();
        $spjBelumLengkap = Spj::where('status_kelengkapan', 'belum_lengkap')->count();

        $totalDesa = User::where('role', 'desa')->count();
        $totalDokumen = SpjDocument::count();
        $totalAnggaran = Spj::sum('anggaran');

        $completionRate = $totalSpj > 0 ? round(($spjLengkap / $totalSpj) * 100, 2) : 0;

        return response()->json([
            'success' => true,
            'message' => 'Dashboard statistics retrieved successfully',
            'data' => [
                'total_spj' => $totalSpj,
                'spj_lengkap' => $spjLengkap,
                'spj_pending' => $spjPending,
                'spj_belum_lengkap' => $spjBelumLengkap,
                'total_desa' => $totalDesa,
                'total_dokumen' => $totalDokumen,
                'total_anggaran' => $totalAnggaran,
                'completion_rate' => $completionRate,
                'status_breakdown' => [
                    'pending' => [
                        'count' => $spjPending,
                        'percentage' => $totalSpj > 0 ? round(($spjPending / $totalSpj) * 100, 2) : 0
                    ],
                    'lengkap' => [
                        'count' => $spjLengkap,
                        'percentage' => $completionRate
                    ],
                    'belum_lengkap' => [
                        'count' => $spjBelumLengkap,
                        'percentage' => $totalSpj > 0 ? round(($spjBelumLengkap / $totalSpj) * 100, 2) : 0
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get dashboard statistics for specific desa
     */
    public function desa(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $totalSpj = Spj::where('user_id', $userId)->count();
        $spjLengkap = Spj::where('user_id', $userId)->where('status_kelengkapan', 'lengkap')->count();
        $spjPending = Spj::where('user_id', $userId)->where('status_kelengkapan', 'pending')->count();
        $spjBelumLengkap = Spj::where('user_id', $userId)->where('status_kelengkapan', 'belum_lengkap')->count();

        $totalDokumen = SpjDocument::where('uploaded_by', $userId)->count();
        $totalAnggaran = Spj::where('user_id', $userId)->sum('anggaran');

        $completionRate = $totalSpj > 0 ? round(($spjLengkap / $totalSpj) * 100, 2) : 0;

        return response()->json([
            'success' => true,
            'message' => 'Desa dashboard statistics retrieved successfully',
            'data' => [
                'total_spj' => $totalSpj,
                'spj_lengkap' => $spjLengkap,
                'spj_pending' => $spjPending,
                'spj_belum_lengkap' => $spjBelumLengkap,
                'total_dokumen' => $totalDokumen,
                'total_anggaran' => $totalAnggaran,
                'completion_rate' => $completionRate,
                'status_breakdown' => [
                    'pending' => [
                        'count' => $spjPending,
                        'percentage' => $totalSpj > 0 ? round(($spjPending / $totalSpj) * 100, 2) : 0
                    ],
                    'lengkap' => [
                        'count' => $spjLengkap,
                        'percentage' => $completionRate
                    ],
                    'belum_lengkap' => [
                        'count' => $spjBelumLengkap,
                        'percentage' => $totalSpj > 0 ? round(($spjBelumLengkap / $totalSpj) * 100, 2) : 0
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get simple dashboard statistics (total SPJ and SPJ lengkap only)
     */
    public function simple(): JsonResponse
    {
        $totalSpj = Spj::count();
        $spjLengkap = Spj::where('status_kelengkapan', 'lengkap')->count();

        return response()->json([
            'success' => true,
            'message' => 'Simple dashboard statistics retrieved successfully',
            'data' => [
                'total_spj' => $totalSpj,
                'spj_lengkap' => $spjLengkap
            ]
        ]);
    }

    /**
     * Get chart data for pie chart
     */
    public function chartData(): JsonResponse
    {
        $spjPending = Spj::where('status_kelengkapan', 'pending')->count();
        $spjLengkap = Spj::where('status_kelengkapan', 'lengkap')->count();
        $spjBelumLengkap = Spj::where('status_kelengkapan', 'belum_lengkap')->count();

        return response()->json([
            'success' => true,
            'message' => 'Chart data retrieved successfully',
            'data' => [
                'labels' => ['Pending', 'Lengkap', 'Belum Lengkap'],
                'datasets' => [
                    [
                        'data' => [$spjPending, $spjLengkap, $spjBelumLengkap],
                        'backgroundColor' => ['#FBBF24', '#34D399', '#F87171'],
                        'borderColor' => ['#F59E0B', '#10B981', '#EF4444'],
                        'borderWidth' => 2
                    ]
                ]
            ]
        ]);
    }

    /**
     * Get chart data for specific desa
     */
    public function desaChartData(Request $request): JsonResponse
    {
        $userId = $request->user()->id;

        $spjPending = Spj::where('user_id', $userId)->where('status_kelengkapan', 'pending')->count();
        $spjLengkap = Spj::where('user_id', $userId)->where('status_kelengkapan', 'lengkap')->count();
        $spjBelumLengkap = Spj::where('user_id', $userId)->where('status_kelengkapan', 'belum_lengkap')->count();

        return response()->json([
            'success' => true,
            'message' => 'Desa chart data retrieved successfully',
            'data' => [
                'labels' => ['Pending', 'Lengkap', 'Belum Lengkap'],
                'datasets' => [
                    [
                        'data' => [$spjPending, $spjLengkap, $spjBelumLengkap],
                        'backgroundColor' => ['#FBBF24', '#34D399', '#F87171'],
                        'borderColor' => ['#F59E0B', '#10B981', '#EF4444'],
                        'borderWidth' => 2
                    ]
                ]
            ]
        ]);
    }
}
