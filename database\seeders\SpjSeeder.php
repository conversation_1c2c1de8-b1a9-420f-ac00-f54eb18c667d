<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Spj;
use App\Models\User;

class SpjSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user for creating sample SPJ
        $adminUser = User::where('role', 'admin')->first();

        if ($adminUser) {
            // Create sample SPJ data
            Spj::create([
                'nama_kegiatan' => 'Pembangunan Jalan Desa',
                'tanggal_kegiatan' => '2024-01-15',
                'anggaran' => 50000000,
                'keterangan' => 'Pembangunan jalan desa sepanjang 500 meter dengan material beton untuk meningkatkan akses transportasi warga.',
                'user_id' => $adminUser->id,
            ]);

            Spj::create([
                'nama_kegiatan' => 'Renovasi Balai Desa',
                'tanggal_kegiatan' => '2024-02-10',
                'anggaran' => 25000000,
                'keterangan' => 'Renovasi balai desa meliputi perbaikan atap, cat ulang, dan penambahan fasilitas.',
                'user_id' => $adminUser->id,
            ]);

            Spj::create([
                'nama_kegiatan' => 'Program Bantuan Sosial',
                'tanggal_kegiatan' => '2024-03-05',
                'anggaran' => 15000000,
                'keterangan' => 'Program bantuan sosial untuk keluarga kurang mampu berupa sembako dan bantuan tunai.',
                'user_id' => $adminUser->id,
            ]);

            Spj::create([
                'nama_kegiatan' => 'Pelatihan UMKM',
                'tanggal_kegiatan' => '2024-03-20',
                'anggaran' => 8000000,
                'keterangan' => 'Pelatihan pengembangan usaha mikro kecil menengah untuk meningkatkan ekonomi masyarakat.',
                'user_id' => $adminUser->id,
            ]);

            Spj::create([
                'nama_kegiatan' => 'Posyandu Balita',
                'tanggal_kegiatan' => '2024-04-01',
                'anggaran' => 3000000,
                'keterangan' => 'Kegiatan posyandu rutin untuk pemeriksaan kesehatan balita dan ibu hamil.',
                'user_id' => $adminUser->id,
            ]);
        }
    }
}
