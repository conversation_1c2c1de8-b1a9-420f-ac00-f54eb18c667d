<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Spj;
use App\Models\SpjDocument;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class MonitoringSpjController extends Controller
{
    /**
     * Get all SPJ with pagination and filtering
     */
    public function index(Request $request): JsonResponse
    {
        $query = Spj::with(['user', 'documents']);

        // Filter by status
        if ($request->has('status') && $request->status !== '') {
            $query->where('status_kelengkapan', $request->status);
        }

        // Filter by desa
        if ($request->has('desa_id') && $request->desa_id !== '') {
            $query->where('user_id', $request->desa_id);
        }

        // Search by nama kegiatan
        if ($request->has('search') && $request->search !== '') {
            $query->where('nama_kegiatan', 'like', '%' . $request->search . '%');
        }

        // Sort by created_at desc by default
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $spjs = $query->paginate($perPage);

        // Transform data
        $spjs->getCollection()->transform(function ($spj) {
            return [
                'id' => $spj->id,
                'nama_kegiatan' => $spj->nama_kegiatan,
                'tanggal_kegiatan' => $spj->tanggal_kegiatan,
                'anggaran' => $spj->anggaran,
                'formatted_anggaran' => $spj->formatted_anggaran,
                'keterangan' => $spj->keterangan,
                'status_kelengkapan' => $spj->status_kelengkapan,
                'formatted_status' => $spj->formatted_status,
                'status_badge_class' => $spj->status_badge_class,
                'created_at' => $spj->created_at,
                'updated_at' => $spj->updated_at,
                'desa' => [
                    'id' => $spj->user->id,
                    'name' => $spj->user->name,
                    'email' => $spj->user->email,
                ],
                'documents_count' => $spj->documents->count(),
                'documents' => $spj->documents->map(function ($doc) {
                    return [
                        'id' => $doc->id,
                        'document_type' => $doc->document_type,
                        'file_name' => $doc->file_name,
                        'file_path' => $doc->file_path,
                        'file_size' => $doc->file_size,
                        'uploaded_at' => $doc->created_at,
                        'uploaded_by' => [
                            'id' => $doc->uploader->id,
                            'name' => $doc->uploader->name,
                        ]
                    ];
                })
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'SPJ monitoring data retrieved successfully',
            'data' => $spjs->items(),
            'pagination' => [
                'current_page' => $spjs->currentPage(),
                'last_page' => $spjs->lastPage(),
                'per_page' => $spjs->perPage(),
                'total' => $spjs->total(),
                'from' => $spjs->firstItem(),
                'to' => $spjs->lastItem(),
            ],
            'filters' => [
                'status' => $request->get('status'),
                'desa_id' => $request->get('desa_id'),
                'search' => $request->get('search'),
                'sort_by' => $sortBy,
                'sort_order' => $sortOrder,
            ]
        ]);
    }

    /**
     * Get specific SPJ with complete details
     */
    public function show(Spj $spj): JsonResponse
    {
        $spj->load(['user', 'documents.uploader']);

        $data = [
            'id' => $spj->id,
            'nama_kegiatan' => $spj->nama_kegiatan,
            'tanggal_kegiatan' => $spj->tanggal_kegiatan,
            'anggaran' => $spj->anggaran,
            'formatted_anggaran' => $spj->formatted_anggaran,
            'keterangan' => $spj->keterangan,
            'status_kelengkapan' => $spj->status_kelengkapan,
            'formatted_status' => $spj->formatted_status,
            'status_badge_class' => $spj->status_badge_class,
            'created_at' => $spj->created_at,
            'updated_at' => $spj->updated_at,
            'desa' => [
                'id' => $spj->user->id,
                'name' => $spj->user->name,
                'email' => $spj->user->email,
                'role' => $spj->user->role,
            ],
            'documents' => $spj->documents->map(function ($doc) {
                return [
                    'id' => $doc->id,
                    'document_type' => $doc->document_type,
                    'file_name' => $doc->file_name,
                    'file_path' => $doc->file_path,
                    'file_size' => $doc->file_size,
                    'formatted_file_size' => $this->formatFileSize($doc->file_size),
                    'uploaded_at' => $doc->created_at,
                    'uploaded_by' => [
                        'id' => $doc->uploader->id,
                        'name' => $doc->uploader->name,
                        'email' => $doc->uploader->email,
                    ]
                ];
            }),
            'documents_count' => $spj->documents->count(),
            'document_types' => $spj->documents->pluck('document_type')->unique()->values(),
        ];

        return response()->json([
            'success' => true,
            'message' => 'SPJ details retrieved successfully',
            'data' => $data
        ]);
    }

    /**
     * Update SPJ status (admin only)
     */
    public function updateStatus(Request $request, Spj $spj): JsonResponse
    {
        $request->validate([
            'status_kelengkapan' => ['required', Rule::in(['pending', 'lengkap', 'belum_lengkap'])]
        ]);

        $spj->update([
            'status_kelengkapan' => $request->status_kelengkapan
        ]);

        $spj->load(['user', 'documents']);

        return response()->json([
            'success' => true,
            'message' => 'SPJ status updated successfully',
            'data' => [
                'id' => $spj->id,
                'nama_kegiatan' => $spj->nama_kegiatan,
                'status_kelengkapan' => $spj->status_kelengkapan,
                'formatted_status' => $spj->formatted_status,
                'status_badge_class' => $spj->status_badge_class,
                'updated_at' => $spj->updated_at,
            ]
        ]);
    }

    /**
     * Get SPJ documents
     */
    public function documents(Spj $spj): JsonResponse
    {
        $documents = $spj->documents()->with('uploader')->get();

        $data = $documents->map(function ($doc) {
            return [
                'id' => $doc->id,
                'document_type' => $doc->document_type,
                'file_name' => $doc->file_name,
                'file_path' => $doc->file_path,
                'file_size' => $doc->file_size,
                'formatted_file_size' => $this->formatFileSize($doc->file_size),
                'uploaded_at' => $doc->created_at,
                'uploaded_by' => [
                    'id' => $doc->uploader->id,
                    'name' => $doc->uploader->name,
                    'email' => $doc->uploader->email,
                ]
            ];
        });

        return response()->json([
            'success' => true,
            'message' => 'SPJ documents retrieved successfully',
            'data' => $data,
            'total_documents' => $documents->count(),
            'document_types' => $documents->pluck('document_type')->unique()->values(),
        ]);
    }

    /**
     * Get monitoring statistics
     */
    public function statistics(): JsonResponse
    {
        $totalSpj = Spj::count();
        $spjPending = Spj::where('status_kelengkapan', 'pending')->count();
        $spjLengkap = Spj::where('status_kelengkapan', 'lengkap')->count();
        $spjBelumLengkap = Spj::where('status_kelengkapan', 'belum_lengkap')->count();

        $totalDesa = User::where('role', 'desa')->count();
        $totalDokumen = SpjDocument::count();
        $totalAnggaran = Spj::sum('anggaran');

        // Recent activities
        $recentSpjs = Spj::with('user')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($spj) {
                return [
                    'id' => $spj->id,
                    'nama_kegiatan' => $spj->nama_kegiatan,
                    'status_kelengkapan' => $spj->status_kelengkapan,
                    'formatted_status' => $spj->formatted_status,
                    'desa_name' => $spj->user->name,
                    'created_at' => $spj->created_at,
                    'time_ago' => $spj->created_at->diffForHumans(),
                ];
            });

        // Top desa by SPJ count
        $topDesa = User::where('role', 'desa')
            ->withCount('spjs')
            ->orderBy('spjs_count', 'desc')
            ->take(5)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'spj_count' => $user->spjs_count,
                ];
            });

        return response()->json([
            'success' => true,
            'message' => 'Monitoring statistics retrieved successfully',
            'data' => [
                'overview' => [
                    'total_spj' => $totalSpj,
                    'spj_pending' => $spjPending,
                    'spj_lengkap' => $spjLengkap,
                    'spj_belum_lengkap' => $spjBelumLengkap,
                    'total_desa' => $totalDesa,
                    'total_dokumen' => $totalDokumen,
                    'total_anggaran' => $totalAnggaran,
                    'completion_rate' => $totalSpj > 0 ? round(($spjLengkap / $totalSpj) * 100, 2) : 0,
                ],
                'status_breakdown' => [
                    'pending' => [
                        'count' => $spjPending,
                        'percentage' => $totalSpj > 0 ? round(($spjPending / $totalSpj) * 100, 2) : 0
                    ],
                    'lengkap' => [
                        'count' => $spjLengkap,
                        'percentage' => $totalSpj > 0 ? round(($spjLengkap / $totalSpj) * 100, 2) : 0
                    ],
                    'belum_lengkap' => [
                        'count' => $spjBelumLengkap,
                        'percentage' => $totalSpj > 0 ? round(($spjBelumLengkap / $totalSpj) * 100, 2) : 0
                    ]
                ],
                'recent_activities' => $recentSpjs,
                'top_desa' => $topDesa,
            ]
        ]);
    }

    /**
     * Get list of desa for filtering
     */
    public function desaList(): JsonResponse
    {
        $desas = User::where('role', 'desa')
            ->withCount('spjs')
            ->orderBy('name')
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'spj_count' => $user->spjs_count,
                ];
            });

        return response()->json([
            'success' => true,
            'message' => 'Desa list retrieved successfully',
            'data' => $desas
        ]);
    }

    /**
     * Format file size to human readable format
     */
    private function formatFileSize($bytes): string
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }
}
