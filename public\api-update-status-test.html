<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiPanda Update SPJ Status API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>

<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">SiPanda Update SPJ Status API Test</h1>

        <!-- Authentication Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Authentication (Admin Required)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input type="email" id="login-email" value="<EMAIL>"
                        class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                    <input type="password" id="login-password" value="password"
                        class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
            </div>
            <div class="flex space-x-4">
                <button onclick="login()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Login
                </button>
                <button onclick="logout()" class="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600">
                    Logout
                </button>
            </div>
            <div id="auth-status" class="mt-4 p-3 rounded hidden">
                <!-- Auth status will be shown here -->
            </div>
        </div>

        <!-- SPJ List for Status Update -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">SPJ List - Select to Update Status</h2>
            <button onclick="loadSpjList()" class="mb-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Load SPJ List
            </button>
            <div id="spj-list" class="space-y-4">
                <p class="text-gray-500">Click "Load SPJ List" to see available SPJ...</p>
            </div>
        </div>

        <!-- Update Status Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Update SPJ Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">SPJ ID</label>
                    <input type="number" id="update-spj-id" placeholder="Enter SPJ ID"
                        class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                    <select id="update-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">Select Status</option>
                        <option value="pending">Pending</option>
                        <option value="lengkap">Lengkap</option>
                        <option value="belum_lengkap">Belum Lengkap</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button onclick="updateSpjStatus()"
                        class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Update Status
                    </button>
                </div>
            </div>
            <div id="update-result" class="mt-4 p-3 rounded hidden">
                <!-- Update result will be shown here -->
            </div>
        </div>

        <!-- Bulk Status Update -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Bulk Status Update (Demo)</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">SPJ IDs (comma separated)</label>
                    <input type="text" id="bulk-spj-ids" placeholder="1,2,3"
                        class="w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                    <select id="bulk-status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">Select Status</option>
                        <option value="pending">Pending</option>
                        <option value="lengkap">Lengkap</option>
                        <option value="belum_lengkap">Belum Lengkap</option>
                    </select>
                </div>
            </div>
            <button onclick="bulkUpdateStatus()" class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                Bulk Update Status
            </button>
            <div id="bulk-result" class="mt-4 p-3 rounded hidden">
                <!-- Bulk update result will be shown here -->
            </div>
        </div>

        <!-- API Response -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">API Response</h2>
            <pre id="api-response" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
Click any button above to see API response here...
            </pre>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8001/api';
        const WEB_BASE = 'http://127.0.0.1:8001';
        let authToken = localStorage.getItem('auth_token');

        function displayResponse(data) {
            document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
        }

        function showAuthStatus(message, isSuccess = true) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.className = `mt-4 p-3 rounded ${isSuccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('hidden');
        }

        function showUpdateResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('update-result');
            resultDiv.className = `mt-4 p-3 rounded ${isSuccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            resultDiv.textContent = message;
            resultDiv.classList.remove('hidden');
        }

        function showBulkResult(message, isSuccess = true) {
            const resultDiv = document.getElementById('bulk-result');
            resultDiv.className = `mt-4 p-3 rounded ${isSuccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`;
            resultDiv.textContent = message;
            resultDiv.classList.remove('hidden');
        }

        async function login() {
            try {
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;

                const response = await fetch(`${WEB_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ email, password })
                });

                const data = await response.json();

                if (response.ok) {
                    // For demo purposes, we'll create a token manually
                    // In real implementation, you'd get this from login response
                    authToken = 'demo_admin_token_' + Date.now();
                    localStorage.setItem('auth_token', authToken);
                    showAuthStatus('Login successful! You can now update SPJ status.', true);
                } else {
                    showAuthStatus('Login failed: ' + (data.message || 'Invalid credentials'), false);
                }

                displayResponse(data);
            } catch (error) {
                console.error('Login error:', error);
                showAuthStatus('Login error: ' + error.message, false);
                displayResponse({ error: error.message });
            }
        }

        function logout() {
            authToken = null;
            localStorage.removeItem('auth_token');
            showAuthStatus('Logged out successfully', true);
        }

        async function loadSpjList() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/spj`);
                const data = await response.json();

                const listContainer = document.getElementById('spj-list');
                listContainer.innerHTML = '';

                if (data.data.length === 0) {
                    listContainer.innerHTML = '<p class="text-gray-500">No SPJ found.</p>';
                } else {
                    data.data.forEach(spj => {
                        const spjCard = document.createElement('div');
                        spjCard.className = 'border border-gray-200 rounded-lg p-4 cursor-pointer hover:bg-gray-50';
                        spjCard.onclick = () => {
                            document.getElementById('update-spj-id').value = spj.id;
                        };
                        spjCard.innerHTML = `
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="font-semibold text-gray-900">ID: ${spj.id} - ${spj.nama_kegiatan}</h3>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${spj.status_badge_class}">
                                    ${spj.formatted_status}
                                </span>
                            </div>
                            <p class="text-sm text-gray-600 mb-2">Desa: ${spj.desa.name}</p>
                            <p class="text-sm text-gray-600 mb-2">Anggaran: ${spj.formatted_anggaran}</p>
                            <p class="text-xs text-gray-500">Click to select for status update</p>
                        `;
                        listContainer.appendChild(spjCard);
                    });
                }

                displayResponse(data);
            } catch (error) {
                console.error('Error loading SPJ list:', error);
                displayResponse({ error: error.message });
            }
        }

        async function updateSpjStatus() {
            try {
                if (!authToken) {
                    showUpdateResult('Please login first as admin', false);
                    return;
                }

                const spjId = document.getElementById('update-spj-id').value;
                const status = document.getElementById('update-status').value;

                if (!spjId || !status) {
                    showUpdateResult('Please enter SPJ ID and select status', false);
                    return;
                }

                const response = await fetch(`${API_BASE}/monitoring/spj/${spjId}/status`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({
                        status_kelengkapan: status
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    showUpdateResult(`SPJ status updated successfully to: ${data.data.formatted_status}`, true);
                    // Reload SPJ list to show updated status
                    loadSpjList();
                } else {
                    showUpdateResult('Update failed: ' + (data.message || 'Unknown error'), false);
                }

                displayResponse(data);
            } catch (error) {
                console.error('Error updating SPJ status:', error);
                showUpdateResult('Update error: ' + error.message, false);
                displayResponse({ error: error.message });
            }
        }

        async function bulkUpdateStatus() {
            try {
                if (!authToken) {
                    showBulkResult('Please login first as admin', false);
                    return;
                }

                const spjIds = document.getElementById('bulk-spj-ids').value.split(',').map(id => id.trim()).filter(id => id);
                const status = document.getElementById('bulk-status').value;

                if (spjIds.length === 0 || !status) {
                    showBulkResult('Please enter SPJ IDs and select status', false);
                    return;
                }

                const results = [];
                let successCount = 0;
                let errorCount = 0;

                for (const spjId of spjIds) {
                    try {
                        const response = await fetch(`${API_BASE}/monitoring/spj/${spjId}/status`, {
                            method: 'PATCH',
                            headers: {
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                                'Authorization': `Bearer ${authToken}`
                            },
                            body: JSON.stringify({
                                status_kelengkapan: status
                            })
                        });

                        const data = await response.json();

                        if (response.ok) {
                            results.push({ spjId, success: true, data });
                            successCount++;
                        } else {
                            results.push({ spjId, success: false, error: data.message });
                            errorCount++;
                        }
                    } catch (error) {
                        results.push({ spjId, success: false, error: error.message });
                        errorCount++;
                    }
                }

                showBulkResult(`Bulk update completed: ${successCount} success, ${errorCount} errors`, successCount > 0);
                displayResponse({ bulk_results: results, summary: { success: successCount, errors: errorCount } });

                // Reload SPJ list to show updated statuses
                loadSpjList();
            } catch (error) {
                console.error('Error in bulk update:', error);
                showBulkResult('Bulk update error: ' + error.message, false);
                displayResponse({ error: error.message });
            }
        }

        // Check auth status on page load
        window.addEventListener('DOMContentLoaded', function () {
            if (authToken) {
                showAuthStatus('You are logged in and can update SPJ status', true);
            } else {
                showAuthStatus('Please login as admin to update SPJ status', false);
            }
            loadSpjList();
        });
    </script>
</body>

</html>