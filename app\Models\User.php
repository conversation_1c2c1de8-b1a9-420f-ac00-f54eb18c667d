<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'username',
        'email',
        'password',
        'role',
        'nama_kecamatan',
        'nama_desa',
        'alamat_desa',
        'nama_kepala_desa',
        'no_handphone',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the SPJs for the user.
     */
    public function spjs(): HasMany
    {
        return $this->hasMany(\App\Models\Spj::class);
    }

    /**
     * Check if user is admin
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    /**
     * Check if user is desa
     */
    public function isDesa(): bool
    {
        return $this->role === 'desa';
    }

    /**
     * Scope for desa users only
     */
    public function scopeDesa($query)
    {
        return $query->where('role', 'desa');
    }

    /**
     * Scope for admin users only
     */
    public function scopeAdmin($query)
    {
        return $query->where('role', 'admin');
    }
}
