@extends('layouts.app')

@section('title', 'Tambah Desa - SiPanda')

@section('breadcrumb')
    <span>SiPanda</span>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <a href="{{ route('desa.index') }}" class="text-gray-500 hover:text-gray-700">Manajemen Desa</a>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <span class="text-gray-900 font-medium">Tambah Desa</span>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <!-- Page Title -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Tambah Desa Baru</h1>
                    <p class="text-gray-600 mt-1">Masukkan informasi desa yang akan ditambahkan.</p>
                </div>
                <a href="{{ route('desa.index') }}"
                    class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    <span>Kembali</span>
                </a>
            </div>
        </div>

        <!-- Form -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <form action="{{ route('desa.store') }}" method="POST">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nama -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" value="{{ old('name') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                            placeholder="Masukkan nama">
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Username -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            Username <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="username" id="username" value="{{ old('username') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('username') border-red-500 @enderror"
                            placeholder="Masukkan username">
                        @error('username')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password <span class="text-red-500">*</span>
                        </label>
                        <input type="password" name="password" id="password"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror"
                            placeholder="Masukkan password">
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Nama Kecamatan -->
                    <div>
                        <label for="nama_kecamatan" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Kecamatan <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="nama_kecamatan" id="nama_kecamatan" value="{{ old('nama_kecamatan') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('nama_kecamatan') border-red-500 @enderror"
                            placeholder="Masukkan nama kecamatan">
                        @error('nama_kecamatan')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Nama Desa -->
                    <div>
                        <label for="nama_desa" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Desa <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="nama_desa" id="nama_desa" value="{{ old('nama_desa') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('nama_desa') border-red-500 @enderror"
                            placeholder="Masukkan nama desa">
                        @error('nama_desa')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Nama Kepala Desa -->
                    <div>
                        <label for="nama_kepala_desa" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Kepala Desa <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="nama_kepala_desa" id="nama_kepala_desa"
                            value="{{ old('nama_kepala_desa') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('nama_kepala_desa') border-red-500 @enderror"
                            placeholder="Masukkan nama kepala desa">
                        @error('nama_kepala_desa')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- No. Handphone -->
                    <div>
                        <label for="no_handphone" class="block text-sm font-medium text-gray-700 mb-2">
                            No. Handphone <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="no_handphone" id="no_handphone" value="{{ old('no_handphone') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('no_handphone') border-red-500 @enderror"
                            placeholder="Masukkan no. handphone">
                        @error('no_handphone')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Alamat Desa -->
                <div class="mt-6">
                    <label for="alamat_desa" class="block text-sm font-medium text-gray-700 mb-2">
                        Alamat Desa <span class="text-red-500">*</span>
                    </label>
                    <textarea name="alamat_desa" id="alamat_desa" rows="4"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('alamat_desa') border-red-500 @enderror"
                        placeholder="Masukkan alamat lengkap desa">{{ old('alamat_desa') }}</textarea>
                    @error('alamat_desa')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Submit Button -->
                <div class="mt-6 flex items-center justify-end space-x-3">
                    <a href="{{ route('desa.index') }}"
                        class="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400">
                        Batal
                    </a>
                    <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                        Simpan
                    </button>
                </div>
            </form>
        </div>
    </div>
@endsection
