<?php

namespace App\Http\Controllers;

use App\Models\Spj;
use App\Models\SpjDocument;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class SpjController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Desa hanya bisa melihat SPJ mereka sendiri
        $spjs = Spj::with('user')
            ->where('user_id', Auth::id())
            ->latest()
            ->paginate(10);
        return view('spj.index', compact('spjs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('spj.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_kegiatan' => 'required|string|max:255',
            'tanggal_kegiatan' => 'required|date',
            'anggaran' => 'required|numeric|min:0',
            'keterangan' => 'nullable|string',
        ]);

        $data = $request->all();
        $data['user_id'] = Auth::id();

        Spj::create($data);

        return redirect()->route('spj.index')
            ->with('success', 'Data SPJ berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Spj $spj)
    {
        $spj->load(['documents.uploader']);
        $documentTypes = SpjDocument::getDocumentTypes();
        return view('spj.show', compact('spj', 'documentTypes'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Spj $spj)
    {
        return view('spj.edit', compact('spj'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Spj $spj)
    {
        $request->validate([
            'nama_kegiatan' => 'required|string|max:255',
            'tanggal_kegiatan' => 'required|date',
            'anggaran' => 'required|numeric|min:0',
            'keterangan' => 'nullable|string',
        ]);

        $spj->update($request->all());

        return redirect()->route('spj.index')
            ->with('success', 'Data SPJ berhasil diperbarui.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Spj $spj)
    {
        // Delete all associated documents first
        foreach ($spj->documents as $document) {
            Storage::delete($document->file_path);
            $document->delete();
        }

        $spj->delete();

        return redirect()->route('spj.index')
            ->with('success', 'Data SPJ berhasil dihapus.');
    }

    /**
     * Upload document for SPJ
     */
    public function uploadDocument(Request $request, Spj $spj)
    {
        // Simple validation first
        $request->validate([
            'document_type' => 'required',
            'file' => 'required|file|max:10240', // 10MB max
        ]);

        if (!$request->hasFile('file')) {
            return redirect()->route('spj.show', $spj)
                ->with('error', 'File tidak ditemukan.');
        }

        try {
            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $fileName = time() . '_' . $originalName;

            // Store file
            $filePath = $file->storeAs('spj-documents', $fileName, 'public');

            if (!$filePath) {
                return redirect()->route('spj.show', $spj)
                    ->with('error', 'Gagal menyimpan file.');
            }

            // Create database record
            $document = SpjDocument::create([
                'spj_id' => $spj->id,
                'document_type' => $request->document_type,
                'document_name' => $request->document_name,
                'original_name' => $originalName,
                'file_name' => $fileName,
                'file_path' => $filePath,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'uploaded_by' => Auth::id(),
            ]);

            return redirect()->route('spj.show', $spj)
                ->with('success', 'Dokumen berhasil diupload. ID: ' . $document->id);
        } catch (\Exception $e) {
            return redirect()->route('spj.show', $spj)
                ->with('error', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Download document
     */
    public function downloadDocument(SpjDocument $spjDocument)
    {
        if (Storage::exists($spjDocument->file_path)) {
            return Storage::download($spjDocument->file_path, $spjDocument->original_name);
        }

        return redirect()->back()->with('error', 'File tidak ditemukan.');
    }

    /**
     * Delete document
     */
    public function deleteDocument(SpjDocument $spjDocument)
    {
        Storage::delete($spjDocument->file_path);
        $spjDocument->delete();

        return redirect()->route('spj.show', $spjDocument->spj)
            ->with('success', 'Dokumen berhasil dihapus.');
    }
}
