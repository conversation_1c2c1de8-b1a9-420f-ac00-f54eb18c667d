<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SiPanda API Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-8">SiPanda Dashboard API Test</h1>
        
        <!-- Simple Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Simple Statistics</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total SPJ</p>
                    <p class="text-2xl font-bold text-blue-600" id="simple-total-spj">-</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Lengkap</p>
                    <p class="text-2xl font-bold text-green-600" id="simple-spj-lengkap">-</p>
                </div>
            </div>
            <button onclick="loadSimpleStats()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Load Simple Stats
            </button>
        </div>

        <!-- Complete Statistics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Complete Statistics</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="bg-blue-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total SPJ</p>
                    <p class="text-2xl font-bold text-blue-600" id="complete-total-spj">-</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Lengkap</p>
                    <p class="text-2xl font-bold text-green-600" id="complete-spj-lengkap">-</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Pending</p>
                    <p class="text-2xl font-bold text-yellow-600" id="complete-spj-pending">-</p>
                </div>
                <div class="bg-red-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">SPJ Belum Lengkap</p>
                    <p class="text-2xl font-bold text-red-600" id="complete-spj-belum-lengkap">-</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-purple-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total Desa</p>
                    <p class="text-2xl font-bold text-purple-600" id="complete-total-desa">-</p>
                </div>
                <div class="bg-indigo-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Total Dokumen</p>
                    <p class="text-2xl font-bold text-indigo-600" id="complete-total-dokumen">-</p>
                </div>
                <div class="bg-pink-50 rounded-lg p-4">
                    <p class="text-sm text-gray-600">Completion Rate</p>
                    <p class="text-2xl font-bold text-pink-600" id="complete-completion-rate">-</p>
                </div>
            </div>
            <button onclick="loadCompleteStats()" class="mt-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                Load Complete Stats
            </button>
        </div>

        <!-- Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Status Distribution Chart</h2>
            <div class="flex justify-center mb-4" style="height: 300px;">
                <canvas id="statusChart"></canvas>
            </div>
            <button onclick="loadChartData()" class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                Load Chart Data
            </button>
        </div>

        <!-- API Response -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">API Response</h2>
            <pre id="api-response" class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
Click any button above to see API response here...
            </pre>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8000/api';
        let chart = null;

        function displayResponse(data) {
            document.getElementById('api-response').textContent = JSON.stringify(data, null, 2);
        }

        async function loadSimpleStats() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/simple`);
                const data = await response.json();
                
                document.getElementById('simple-total-spj').textContent = data.data.total_spj;
                document.getElementById('simple-spj-lengkap').textContent = data.data.spj_lengkap;
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading simple stats:', error);
                displayResponse({ error: error.message });
            }
        }

        async function loadCompleteStats() {
            try {
                const response = await fetch(`${API_BASE}/dashboard`);
                const data = await response.json();
                
                document.getElementById('complete-total-spj').textContent = data.data.total_spj;
                document.getElementById('complete-spj-lengkap').textContent = data.data.spj_lengkap;
                document.getElementById('complete-spj-pending').textContent = data.data.spj_pending;
                document.getElementById('complete-spj-belum-lengkap').textContent = data.data.spj_belum_lengkap;
                document.getElementById('complete-total-desa').textContent = data.data.total_desa;
                document.getElementById('complete-total-dokumen').textContent = data.data.total_dokumen;
                document.getElementById('complete-completion-rate').textContent = data.data.completion_rate + '%';
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading complete stats:', error);
                displayResponse({ error: error.message });
            }
        }

        async function loadChartData() {
            try {
                const response = await fetch(`${API_BASE}/dashboard/chart-data`);
                const data = await response.json();
                
                const ctx = document.getElementById('statusChart').getContext('2d');
                
                if (chart) {
                    chart.destroy();
                }
                
                chart = new Chart(ctx, {
                    type: 'pie',
                    data: data.data,
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                        return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
                
                displayResponse(data);
            } catch (error) {
                console.error('Error loading chart data:', error);
                displayResponse({ error: error.message });
            }
        }

        // Load simple stats on page load
        window.addEventListener('DOMContentLoaded', loadSimpleStats);
    </script>
</body>
</html>
