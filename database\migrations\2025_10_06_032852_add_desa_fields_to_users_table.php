<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('role')->default('admin')->after('password');
            $table->string('nama_kecamatan')->nullable()->after('role');
            $table->string('nama_desa')->nullable()->after('nama_kecamatan');
            $table->text('alamat_desa')->nullable()->after('nama_desa');
            $table->string('nama_kepala_desa')->nullable()->after('alamat_desa');
            $table->string('no_handphone')->nullable()->after('nama_kepala_desa');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'role',
                'nama_kecamatan',
                'nama_desa',
                'alamat_desa',
                'nama_kepala_desa',
                'no_handphone'
            ]);
        });
    }
};
