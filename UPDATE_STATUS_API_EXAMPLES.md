# SiPanda Update SPJ Status API Examples

## Overview

API untuk mengubah status SPJ memerlukan autentikasi admin dan menggunakan method PATCH.

**Endpoint:** `PATCH /api/monitoring/spj/{id}/status`

**Authentication:** Required (<PERSON><PERSON> + Admin Role)

**Status Options:**
- `pending` - <PERSON>J baru dibuat, menunggu review
- `lengkap` - SPJ sudah lengkap dan disetujui
- `belum_lengkap` - SPJ belum lengkap, perlu perbaikan

---

## 1. Authentication (Get Token)

### Login to Get Token

**Endpoint:** `POST /api/auth/login`

```bash
curl -X POST "http://127.0.0.1:8001/api/auth/login" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "password"
     }'
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": 1,
            "name": "Admin",
            "email": "<EMAIL>",
            "role": "admin"
        },
        "token": "1|abc123def456...",
        "token_type": "Bearer"
    }
}
```

---

## 2. Update SPJ Status

### Update Single SPJ Status

```bash
# Set your token from login response
TOKEN="1|abc123def456..."

# Update SPJ ID 1 to "lengkap"
curl -X PATCH "http://127.0.0.1:8001/api/monitoring/spj/1/status" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{
       "status_kelengkapan": "lengkap"
     }'
```

**Response:**
```json
{
    "success": true,
    "message": "SPJ status updated successfully",
    "data": {
        "id": 1,
        "nama_kegiatan": "Pembangunan Jalan Desa",
        "status_kelengkapan": "lengkap",
        "formatted_status": "Lengkap",
        "status_badge_class": "bg-green-100 text-green-800",
        "updated_at": "2025-10-06T06:30:15.000000Z"
    }
}
```

### Update to Different Status

```bash
# Update to "belum_lengkap"
curl -X PATCH "http://127.0.0.1:8001/api/monitoring/spj/2/status" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{
       "status_kelengkapan": "belum_lengkap"
     }'

# Update to "pending"
curl -X PATCH "http://127.0.0.1:8001/api/monitoring/spj/3/status" \
     -H "Content-Type: application/json" \
     -H "Accept: application/json" \
     -H "Authorization: Bearer $TOKEN" \
     -d '{
       "status_kelengkapan": "pending"
     }'
```

---

## 3. JavaScript Examples

### Login and Get Token

```javascript
async function loginAndGetToken() {
    try {
        const response = await fetch('http://127.0.0.1:8001/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({
                email: '<EMAIL>',
                password: 'password'
            })
        });

        const data = await response.json();
        
        if (data.success) {
            localStorage.setItem('auth_token', data.data.token);
            console.log('Login successful:', data.data.user);
            return data.data.token;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Login failed:', error);
        throw error;
    }
}
```

### Update SPJ Status

```javascript
async function updateSpjStatus(spjId, newStatus) {
    try {
        const token = localStorage.getItem('auth_token');
        
        if (!token) {
            throw new Error('No authentication token found. Please login first.');
        }

        const response = await fetch(`http://127.0.0.1:8001/api/monitoring/spj/${spjId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                status_kelengkapan: newStatus
            })
        });

        const data = await response.json();
        
        if (data.success) {
            console.log('Status updated successfully:', data.data);
            return data.data;
        } else {
            throw new Error(data.message);
        }
    } catch (error) {
        console.error('Update failed:', error);
        throw error;
    }
}

// Usage examples
updateSpjStatus(1, 'lengkap');
updateSpjStatus(2, 'belum_lengkap');
updateSpjStatus(3, 'pending');
```

### Bulk Update Multiple SPJ

```javascript
async function bulkUpdateSpjStatus(spjIds, newStatus) {
    const results = [];
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
        throw new Error('No authentication token found. Please login first.');
    }

    for (const spjId of spjIds) {
        try {
            const response = await fetch(`http://127.0.0.1:8001/api/monitoring/spj/${spjId}/status`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    status_kelengkapan: newStatus
                })
            });

            const data = await response.json();
            
            results.push({
                spjId,
                success: data.success,
                data: data.success ? data.data : null,
                error: data.success ? null : data.message
            });
        } catch (error) {
            results.push({
                spjId,
                success: false,
                data: null,
                error: error.message
            });
        }
    }

    return results;
}

// Usage example
bulkUpdateSpjStatus([1, 2, 3], 'lengkap').then(results => {
    console.log('Bulk update results:', results);
});
```

---

## 4. Error Handling

### Common Error Responses

#### 401 Unauthorized (No Token)
```json
{
    "success": false,
    "message": "Unauthenticated. Please login first.",
    "error": "Authentication required"
}
```

#### 403 Forbidden (Not Admin)
```json
{
    "success": false,
    "message": "Access denied. Admin privileges required.",
    "error": "Insufficient permissions"
}
```

#### 404 Not Found (Invalid SPJ ID)
```json
{
    "message": "No query results for model [App\\Models\\Spj] 999"
}
```

#### 422 Validation Error (Invalid Status)
```json
{
    "message": "The status kelengkapan field must be one of: pending, lengkap, belum_lengkap.",
    "errors": {
        "status_kelengkapan": [
            "The status kelengkapan field must be one of: pending, lengkap, belum_lengkap."
        ]
    }
}
```

### Error Handling in JavaScript

```javascript
async function updateSpjStatusWithErrorHandling(spjId, newStatus) {
    try {
        const token = localStorage.getItem('auth_token');
        
        const response = await fetch(`http://127.0.0.1:8001/api/monitoring/spj/${spjId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                status_kelengkapan: newStatus
            })
        });

        const data = await response.json();
        
        if (response.status === 401) {
            // Token expired or invalid
            localStorage.removeItem('auth_token');
            throw new Error('Authentication required. Please login again.');
        } else if (response.status === 403) {
            throw new Error('Access denied. Admin privileges required.');
        } else if (response.status === 404) {
            throw new Error(`SPJ with ID ${spjId} not found.`);
        } else if (response.status === 422) {
            throw new Error(`Validation error: ${data.message}`);
        } else if (!response.ok) {
            throw new Error(data.message || 'Unknown error occurred');
        }

        return data.data;
    } catch (error) {
        console.error('Update SPJ status error:', error);
        throw error;
    }
}
```

---

## 5. Complete Workflow Example

```javascript
class SpjStatusManager {
    constructor() {
        this.token = localStorage.getItem('auth_token');
        this.baseUrl = 'http://127.0.0.1:8001/api';
    }

    async login(email, password) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify({ email, password })
        });

        const data = await response.json();
        
        if (data.success) {
            this.token = data.data.token;
            localStorage.setItem('auth_token', this.token);
            return data.data;
        } else {
            throw new Error(data.message);
        }
    }

    async updateStatus(spjId, status) {
        if (!this.token) {
            throw new Error('Not authenticated. Please login first.');
        }

        const response = await fetch(`${this.baseUrl}/monitoring/spj/${spjId}/status`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': `Bearer ${this.token}`
            },
            body: JSON.stringify({
                status_kelengkapan: status
            })
        });

        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Update failed');
        }

        return data.data;
    }

    async getSpjList(filters = {}) {
        const params = new URLSearchParams(filters);
        const response = await fetch(`${this.baseUrl}/monitoring/spj?${params}`);
        const data = await response.json();
        return data.data;
    }
}

// Usage
const manager = new SpjStatusManager();

// Login
await manager.login('<EMAIL>', 'password');

// Get SPJ list
const spjList = await manager.getSpjList({ status: 'pending' });

// Update status
for (const spj of spjList) {
    if (spj.documents_count >= 2) {
        await manager.updateStatus(spj.id, 'lengkap');
        console.log(`Updated SPJ ${spj.id} to lengkap`);
    }
}
```

---

## 6. Testing

### Test Page
Open `http://127.0.0.1:8001/api-update-status-test.html` to test the API interactively.

### Manual Testing Steps
1. Login with admin credentials
2. Load SPJ list to see current statuses
3. Select an SPJ and update its status
4. Verify the status change in the list
5. Test bulk updates with multiple SPJ IDs

### Validation Testing
- Try updating with invalid status values
- Test without authentication token
- Test with non-admin user token
- Test with non-existent SPJ ID
