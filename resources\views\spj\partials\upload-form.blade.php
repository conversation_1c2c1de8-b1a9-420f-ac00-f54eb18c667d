<!-- Upload Document Form -->
<div class="bg-white rounded-lg shadow-sm border border-gray-200 mt-6">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Upload <PERSON><PERSON><PERSON>pan SPJ</h3>
        <p class="text-sm text-gray-600 mt-1">Upload dokumen pendukung untuk kelengkapan SPJ</p>
    </div>

    <form action="{{ route('spj.upload-document', $spj) }}" method="POST" enctype="multipart/form-data" class="p-6">
        @csrf

        <!-- Debug Info -->
        @if ($errors->any())
            <div class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 class="text-red-800 font-medium">Validation Errors:</h4>
                <ul class="mt-2 text-sm text-red-600">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Document Type -->
            <div>
                <label for="document_type" class="block text-sm font-medium text-gray-700 mb-2">
                    Jenis Dokumen <span class="text-red-500">*</span>
                </label>
                <select name="document_type" id="document_type"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('document_type') border-red-500 @enderror"
                    onchange="toggleCustomName()">
                    <option value="">Pilih Jenis Dokumen</option>
                    @foreach ($documentTypes as $key => $label)
                        <option value="{{ $key }}" {{ old('document_type') == $key ? 'selected' : '' }}>
                            {{ $label }}
                        </option>
                    @endforeach
                </select>
                @error('document_type')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Custom Document Name (for 'lainnya' type) -->
            <div id="custom_name_field" style="display: none;">
                <label for="document_name" class="block text-sm font-medium text-gray-700 mb-2">
                    Nama Kelengkapan <span class="text-red-500">*</span>
                </label>
                <input type="text" name="document_name" id="document_name" value="{{ old('document_name') }}"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 @error('document_name') border-red-500 @enderror"
                    placeholder="Masukkan nama kelengkapan">
                @error('document_name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- File Upload -->
            <div class="md:col-span-2">
                <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                    File Dokumen <span class="text-red-500">*</span>
                </label>
                <div
                    class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                    <div class="space-y-1 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none"
                            viewBox="0 0 48 48">
                            <path
                                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                                stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        </svg>
                        <div class="flex text-sm text-gray-600">
                            <label for="file"
                                class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                <span>Upload file</span>
                                <input id="file" name="file" type="file" class="sr-only"
                                    accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx" onchange="showFileName(this)">
                            </label>
                            <p class="pl-1">atau drag and drop</p>
                        </div>
                        <p class="text-xs text-gray-500">PDF, JPG, PNG, DOC, DOCX, XLS, XLSX hingga 10MB</p>
                        <p id="file-name" class="text-sm text-gray-900 font-medium hidden"></p>
                    </div>
                </div>
                @error('file')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>
        </div>

        <!-- Submit Button -->
        <div class="mt-6 flex items-center justify-end">
            <button type="submit"
                class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12">
                    </path>
                </svg>
                <span>Upload Dokumen</span>
            </button>
        </div>
    </form>
</div>

<script>
    function toggleCustomName() {
        const documentType = document.getElementById('document_type').value;
        const customNameField = document.getElementById('custom_name_field');
        const documentNameInput = document.getElementById('document_name');

        if (documentType === 'lainnya') {
            customNameField.style.display = 'block';
            documentNameInput.required = true;
        } else {
            customNameField.style.display = 'none';
            documentNameInput.required = false;
            documentNameInput.value = '';
        }
    }

    function showFileName(input) {
        const fileNameElement = document.getElementById('file-name');
        if (input.files && input.files[0]) {
            fileNameElement.textContent = input.files[0].name;
            fileNameElement.classList.remove('hidden');
        } else {
            fileNameElement.classList.add('hidden');
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        toggleCustomName();
    });
</script>
