@extends('layouts.app')

@section('title', 'Detail Desa - SiPanda')

@section('breadcrumb')
    <span>SiPanda</span>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <a href="{{ route('desa.index') }}" class="text-gray-500 hover:text-gray-700">Manajemen Desa</a>
    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
    </svg>
    <span class="text-gray-900 font-medium">Detail Desa</span>
@endsection

@section('content')
    <div class="max-w-4xl mx-auto">
        <!-- Page Title -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-900">Detail Desa</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap desa {{ $desa->nama_desa }}.</p>
                </div>
                <div class="flex items-center space-x-3">
                    <a href="{{ route('desa.edit', $desa) }}"
                        class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z">
                            </path>
                        </svg>
                        <span>Edit</span>
                    </a>
                    <a href="{{ route('desa.index') }}"
                        class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center space-x-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        <span>Kembali</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Detail Card -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Informasi Desa</h3>
            </div>

            <!-- Content -->
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Nama -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Nama</label>
                        <p class="text-gray-900 font-medium">{{ $desa->name }}</p>
                    </div>

                    <!-- Username -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Username</label>
                        <p class="text-gray-900 font-medium">{{ $desa->username }}</p>
                    </div>

                    <!-- Nama Kecamatan -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Nama Kecamatan</label>
                        <p class="text-gray-900 font-medium">{{ $desa->nama_kecamatan }}</p>
                    </div>

                    <!-- Nama Desa -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Nama Desa</label>
                        <p class="text-gray-900 font-medium">{{ $desa->nama_desa }}</p>
                    </div>

                    <!-- Nama Kepala Desa -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">Nama Kepala Desa</label>
                        <p class="text-gray-900 font-medium">{{ $desa->nama_kepala_desa }}</p>
                    </div>

                    <!-- No. Handphone -->
                    <div>
                        <label class="block text-sm font-medium text-gray-500 mb-1">No. Handphone</label>
                        <p class="text-gray-900 font-medium">{{ $desa->no_handphone }}</p>
                    </div>
                </div>

                <!-- Alamat Desa -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-500 mb-1">Alamat Desa</label>
                    <p class="text-gray-900 font-medium">{{ $desa->alamat_desa }}</p>
                </div>

                <!-- Timestamps -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Dibuat pada</label>
                            <p class="text-gray-700">{{ $desa->created_at->format('d M Y, H:i') }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-500 mb-1">Terakhir diperbarui</label>
                            <p class="text-gray-700">{{ $desa->updated_at->format('d M Y, H:i') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-6 flex items-center justify-end space-x-3">
            <form action="{{ route('desa.destroy', $desa) }}" method="POST" class="inline"
                onsubmit="return confirm('Apakah Anda yakin ingin menghapus data desa ini? Tindakan ini tidak dapat dibatalkan.')">
                @csrf
                @method('DELETE')
                <button type="submit"
                    class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
                        </path>
                    </svg>
                    <span>Hapus Desa</span>
                </button>
            </form>
        </div>
    </div>
@endsection
